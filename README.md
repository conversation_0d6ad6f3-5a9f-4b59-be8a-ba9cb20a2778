# Biormika Stack - Brain Activity Analysis Platform

## What is This?

Biormika Stack is a cloud-based platform that helps neurologists and researchers analyze brain recordings (EEG files) to automatically detect High-Frequency Oscillations (HFOs) - important patterns that can indicate epileptic brain tissue.

### The Medical Problem

- **EEG recordings** capture brain activity from multiple sensors (channels) over time
- **HFOs** are brief (6-100ms) high-frequency (80-500Hz) brain oscillations
- **Manual detection** takes hours per file and is inconsistent between analysts
- **Clinical importance**: HFOs help identify seizure onset zones in epilepsy patients

### Our Solution

An automated pipeline that processes EEG files in the cloud:

```
Upload EDF → Queue Job → Detect HFOs → View Results → Email Notification
   (1GB)      (SQS)      (Algorithm)    (Charts)       (Complete)
```

## System Overview

### What Each Component Does

| Component | Purpose | Key Responsibility |
|-----------|---------|-------------------|
| **Frontend** | Web interface | Upload files, view results, interactive charts |
| **Backend** | API server | Coordinate uploads, manage jobs, track status |
| **HFOProcessor** | Detection engine | Run algorithm, find HFOs, generate results |
| **Infrastructure** | Cloud resources | Storage, queue, database, auto-scaling |

### Data Flow

1. **Upload**: User uploads EDF file through web interface
2. **Store**: File saved to S3, job created in database
3. **Queue**: Job sent to SQS for processing
4. **Process**: ECS container downloads file, runs detection
5. **Results**: CSV/JSON saved to S3, email sent
6. **Display**: Frontend shows interactive visualizations

## Quick Start

### Prerequisites
```bash
# Required tools
node --version  # 20.19+
python --version  # 3.9+
docker --version  # For HFO processor
aws --version  # Configured with 'biormika' profile
cdk --version  # npm install -g aws-cdk
```

### First Time Setup
```bash
# 1. Deploy infrastructure (one-time)
cd Infra
python3 -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
cdk deploy --profile biormika --outputs-file cdk-outputs.json

# 2. Start backend
cd ../Backend
python3 -m venv venv && source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env  # Add S3 bucket from step 1
python run.py  # http://localhost:8000/docs

# 3. Start frontend
cd ../Frontend
npm install
npm run dev  # http://localhost:5173
```

## Essential Commands

### Development
```bash
# Frontend
npm run dev                  # Start dev server
npm run build               # Build for production
npm run lint                # Check code quality

# Backend
python run.py               # Start API server
./build_lambda.sh          # Package for Lambda

# HFO Processor
docker build -t hfo .      # Build container
python processor.py --test # Test locally

# Infrastructure
cdk diff                   # Preview changes
cdk deploy                 # Apply changes
```

### Deployment
```bash
# Deploy everything
./deploy_infra.sh && ./deploy_backend.sh && \
./deploy_frontend.sh && ./deploy_hfo_processor.sh

# Deploy specific component
./deploy_backend.sh        # Update API only
./deploy_frontend.sh       # Update web app only
./deploy_hfo_processor.sh  # Update algorithm only
```

### Monitoring
```bash
# View logs
aws logs tail /ecs/biormika-hfo-processor --profile biormika --follow
aws logs tail /aws/lambda/BiormikaStack-ApiFunction --profile biormika

# Check queue
aws sqs get-queue-attributes \
  --queue-url $(aws sqs list-queues --profile biormika | jq -r '.QueueUrls[0]') \
  --attribute-names All --profile biormika

# ECS status
aws ecs describe-services \
  --cluster BiormikaHFOCluster \
  --services HFOProcessorService \
  --profile biormika | jq '.services[0].runningCount'
```

## Understanding HFO Detection

### The Algorithm

1. **Load EDF**: Read multi-channel brain recordings
2. **Filter**: Apply bandpass filter (80-500 Hz) to isolate HFOs
3. **Detect**: Find segments exceeding threshold (mean + 3σ)
4. **Validate**: Check duration (≥6ms) and merge close events
5. **Output**: Generate CSV with timestamps and amplitudes

### Detection Parameters

```python
{
    "frequency_band": [80, 500],  # Hz - HFO frequency range
    "threshold": 3.0,              # Standard deviations above baseline
    "min_duration_ms": 6,          # Minimum HFO duration
    "min_gap_ms": 10              # Merge events closer than this
}
```

## Project Structure

```
biormika-stack/
├── Frontend/          # React TypeScript web app
├── Backend/           # FastAPI Python API
├── HFOProcessor/      # Detection algorithm (Python)
├── Infra/            # AWS CDK infrastructure (Python)
├── deploy_*.sh       # Deployment scripts
├── CLAUDE.md         # AI assistant context
└── README.md         # This file
```

## Key Technologies

- **Medical**: EDF format, HFO detection, signal processing
- **Frontend**: React, TypeScript, Plotly.js, Redux Toolkit
- **Backend**: FastAPI, Lambda, Boto3, Pydantic
- **Processing**: NumPy, SciPy, PyEDFlib, Docker
- **Cloud**: S3, ECS Fargate, SQS, DynamoDB, CloudFront

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Upload fails | Check S3 CORS, file size <1GB, .edf extension |
| Processing stuck | Check ECS tasks, SQS dead letter queue |
| No results | Verify S3 permissions, check CloudWatch logs |
| CORS errors | Update Backend CORS origins, check CloudFront |

## Environment Variables

### Backend (.env)
```env
S3_BUCKET_NAME=biormika-stack-files-xxxxx
CLOUDFRONT_URL=https://dxxxxx.cloudfront.net
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/xxx
DYNAMODB_TABLE_NAME=BiormikaHFOJobTable
```

### Frontend (vite.config.ts)
```javascript
proxy: {
  '/api': 'http://localhost:8000'  # Development API
}
```

## Performance Specs

- **File Size**: Up to 1GB EDF files
- **Channels**: Processes all channels in parallel
- **Speed**: ~45 seconds per hour of recording
- **Scaling**: 0-10 concurrent processing tasks
- **Memory**: Lambda 3GB, ECS 4GB RAM

## Security Considerations

- EDF files may contain patient data (HIPAA)
- S3 buckets encrypted with AES-256
- All public access blocked
- Presigned URLs expire after 1 hour
- Consider data residency requirements

## Getting Help

- API docs: http://localhost:8000/docs
- Check folder-specific READMEs for details
- Review CLAUDE.md for AI assistance
- AWS costs: Monitor CloudWatch billing alerts

## License

Copyright 2024 Biormika. All rights reserved.