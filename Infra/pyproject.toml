[tool.ruff]
line-length = 88
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "T20",  # flake8-print
    "SIM",  # flake8-simplify
    "RUF",  # Ruff-specific rules
]
ignore = [
    "E501",  # line too long (handled by formatter)
    "T201",  # print statements (useful for debugging)
    "E402",  # module level import not at top of file (needed for sys.path manipulation)
]

[tool.ruff.lint.isort]
known-third-party = ["aws_cdk", "constructs", "boto3"]

[tool.ruff.lint.per-file-ignores]
"infra/config.py" = ["I001"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.black]
line-length = 88
target-version = ["py311"]

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"