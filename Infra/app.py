#!/usr/bin/env python3
import os

import aws_cdk as cdk

from infra.config import (
    AWS_PROFILE,
    DEFAULT_ACCOUNT,
    DEFAULT_REGION,
    STACK_DESCRIPTION,
    STACK_NAME,
)
from infra.infra_stack import InfraStack

# Initialize CDK app
app = cdk.App()

# Set AWS profile if configured
if AWS_PROFILE:
    os.environ["AWS_PROFILE"] = AWS_PROFILE

# Create infrastructure stack
InfraStack(
    app,
    STACK_NAME,
    env=cdk.Environment(account=DEFAULT_ACCOUNT, region=DEFAULT_REGION),
    description=STACK_DESCRIPTION,
)

# Synthesize CloudFormation template
app.synth()
