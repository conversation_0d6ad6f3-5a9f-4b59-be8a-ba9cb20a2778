import os

from .shared_constants import (
    # Project Configuration
    PROJECT_NAME,
    PURPOSE,
    STACK_NAME,
    STACK_DESCRIPTION,
    ENVIRONMENT,
    # AWS Configuration
    AWS_PROFILE,
    AWS_REGION,
    # Email Configuration
    SENDER_EMAIL,
    # File Configuration
    ALLOWED_FILE_EXTENSIONS,
    # Network Configuration
    VITE_DEV_PORT,
    REACT_ALT_PORT,
    FASTAPI_PORT,
    # CORS Configuration
    DEFAULT_ALLOWED_ORIGINS,
    # S3 Configuration
    S3_CORS_MAX_AGE_SECONDS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
    S3_CORS_EXPOSED_HEADERS,
    # Lambda Configuration
    LAMBDA_RUNTIME_PYTHON,
    LAMBDA_ARCHITECTURE,
    LAMBDA_TIMEOUT_SECONDS,
    LAMBDA_MEMORY_SIZE_MB,
    LAMBDA_LOG_RETENTION_DAYS,
    LAMBDA_LOG_GROUP_NAME,
    LAMBDA_HANDLER,
    # API Gateway Configuration
    API_STAGE_NAME,
    API_THROTTLE_RATE_LIMIT,
    API_THROTTLE_BURST_LIMIT,
    API_CORS_MAX_AGE_HOURS,
    # S3 IAM Actions
    S3_ACTIONS_READ_WRITE,
    S3_ACTIONS_PRESIGNED,
)

# Environment Variables
DEFAULT_REGION = os.getenv("CDK_DEFAULT_REGION", AWS_REGION)
DEFAULT_ACCOUNT = os.getenv("CDK_DEFAULT_ACCOUNT", "************")

# File Extensions with Wildcards for S3 Policies
ALLOWED_FILE_PATTERNS = [f"*{ext}" for ext in ALLOWED_FILE_EXTENSIONS]

__all__ = [
    # Project Configuration
    "PROJECT_NAME",
    "PURPOSE",
    "STACK_NAME",
    "STACK_DESCRIPTION",
    "ENVIRONMENT",
    # AWS Configuration
    "AWS_PROFILE",
    "DEFAULT_REGION",
    "DEFAULT_ACCOUNT",
    # Email Configuration
    "SENDER_EMAIL",
    # File Configuration
    "ALLOWED_FILE_EXTENSIONS",
    "ALLOWED_FILE_PATTERNS",
    # Network Configuration
    "VITE_DEV_PORT",
    "REACT_ALT_PORT",
    "FASTAPI_PORT",
    # CORS Configuration
    "DEFAULT_ALLOWED_ORIGINS",
    # S3 Configuration
    "S3_CORS_MAX_AGE_SECONDS",
    "S3_MULTIPART_UPLOAD_CLEANUP_DAYS",
    "S3_CORS_EXPOSED_HEADERS",
    # Lambda Configuration
    "LAMBDA_RUNTIME_PYTHON",
    "LAMBDA_ARCHITECTURE",
    "LAMBDA_TIMEOUT_SECONDS",
    "LAMBDA_MEMORY_SIZE_MB",
    "LAMBDA_LOG_RETENTION_DAYS",
    "LAMBDA_LOG_GROUP_NAME",
    "LAMBDA_HANDLER",
    # API Gateway Configuration
    "API_STAGE_NAME",
    "API_THROTTLE_RATE_LIMIT",
    "API_THROTTLE_BURST_LIMIT",
    "API_CORS_MAX_AGE_HOURS",
    # S3 IAM Actions
    "S3_ACTIONS_READ_WRITE",
    "S3_ACTIONS_PRESIGNED",
]
