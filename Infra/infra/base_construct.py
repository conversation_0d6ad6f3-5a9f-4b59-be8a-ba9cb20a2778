from aws_cdk import CfnOutput, RemovalPolicy
from aws_cdk import aws_iam as iam
from aws_cdk import aws_logs as logs
from constructs import Construct


class BaseConstruct(Construct):
    """Base class for all CDK constructs with common functionality."""

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id)
        self._outputs: dict[str, CfnOutput] = {}

    # Output Management
    def create_output(
        self,
        id: str,
        value: str,
        description: str,
        export_name: str | None = None,
    ) -> CfnOutput:
        """Create and track CloudFormation outputs."""
        output = CfnOutput(
            self,
            id,
            value=value,
            description=description,
            export_name=export_name,
        )
        self._outputs[id] = output
        return output

    # Log Group Creation
    def create_log_group(
        self,
        id: str,
        log_group_name: str,
        retention_days: logs.RetentionDays = logs.RetentionDays.ONE_WEEK,
        removal_policy: RemovalPolicy = RemovalPolicy.DESTROY,
    ) -> logs.LogGroup:
        """Create CloudWatch log group with standard settings."""
        return logs.LogGroup(
            self,
            id,
            log_group_name=log_group_name,
            retention=retention_days,
            removal_policy=removal_policy,
        )

    # IAM Role Creation
    def create_service_role(
        self,
        id: str,
        service_principals: list[str],
        description: str,
        managed_policies: list[iam.IManagedPolicy] | None = None,
    ) -> iam.Role:
        """Create IAM role for AWS services."""
        principals = [iam.ServicePrincipal(sp) for sp in service_principals]
        role = iam.Role(
            self,
            id,
            assumed_by=iam.CompositePrincipal(*principals)
            if len(principals) > 1
            else principals[0],
            description=description,
        )

        if managed_policies:
            for policy in managed_policies:
                role.add_managed_policy(policy)

        return role

    # Permission Grants
    def grant_s3_permissions(
        self,
        role: iam.IRole,
        bucket_arn: str,
        actions: list[str],
        resource_patterns: list[str] | None = None,
    ) -> iam.PolicyStatement:
        """Grant S3 permissions to a role."""
        resources = [bucket_arn]
        if resource_patterns:
            resources.extend(
                [f"{bucket_arn}/{pattern}" for pattern in resource_patterns]
            )
        else:
            resources.append(f"{bucket_arn}/*")

        statement = iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=actions,
            resources=resources,
        )
        role.add_to_policy(statement)
        return statement

    # Environment Variables
    def get_environment_variables(self, **kwargs) -> dict[str, str]:
        """Get environment variables for compute resources."""
        return {k: v for k, v in kwargs.items() if v is not None}
