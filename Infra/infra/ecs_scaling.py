from aws_cdk import Duration
from aws_cdk import aws_applicationautoscaling as autoscaling
from aws_cdk import aws_ecs as ecs
from aws_cdk import aws_sqs as sqs


class ECSAutoScalingConfig:
    """Configuration for ECS auto-scaling based on SQS metrics."""

    @staticmethod
    def configure_queue_depth_scaling(
        service: ecs.FargateService,
        job_queue: sqs.IQueue,
        min_capacity: int = 0,
        max_capacity: int = 10,
    ) -> autoscaling.ScalableTarget:
        """Configure auto-scaling based on SQS queue depth."""
        scaling_target = service.auto_scale_task_count(
            min_capacity=min_capacity,
            max_capacity=max_capacity,
        )

        scaling_target.scale_on_metric(
            "QueueDepthScaling",
            metric=job_queue.metric_approximate_number_of_messages_visible(),
            scaling_steps=[
                autoscaling.ScalingInterval(upper=0, change=0),
                autoscaling.ScalingInterval(lower=1, upper=5, change=1),
                autoscaling.ScalingInterval(lower=5, upper=10, change=2),
                autoscaling.ScalingInterval(lower=10, upper=20, change=3),
                autoscaling.ScalingInterval(lower=20, change=5),
            ],
            adjustment_type=autoscaling.AdjustmentType.EXACT_CAPACITY,
            cooldown=Duration.minutes(2),
            evaluation_periods=1,
        )

        return scaling_target
