# Lambda API construct for serverless backend
import os
import subprocess

from aws_cdk import Duration
from aws_cdk import aws_apigateway as apigw
from aws_cdk import aws_iam as iam
from aws_cdk import aws_lambda as _lambda
from aws_cdk import aws_logs as logs
from constructs import Construct

from .base_construct import BaseConstruct
from .config import (
    API_STAGE_NAME,
    API_THROTTLE_BURST_LIMIT,
    API_THROTTLE_RATE_LIMIT,
    LAMBDA_ARCHITECTURE,
    LAMBDA_HANDLER,
    LAMBDA_LOG_GROUP_NAME,
    LAMBDA_LOG_RETENTION_DAYS,
    LAMBDA_MEMORY_SIZE_MB,
    LAMBDA_RUNTIME_PYTHON,
    LAMBDA_TIMEOUT_SECONDS,
    S3_ACTIONS_READ_WRITE,
)
from .helpers import create_api_cors_options


class LambdaApiConstruct(BaseConstruct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str,
        backend_role: iam.Role,
        allowed_origins: list[str],
    ) -> None:
        super().__init__(scope, construct_id)

        self.bucket_name = bucket_name
        self.backend_role = backend_role
        self.allowed_origins = allowed_origins

        self._create_lambda_infrastructure()
        self._create_api_gateway()
        self._create_outputs()

    def _create_lambda_infrastructure(self) -> None:
        """Create Lambda function with execution role and log group."""
        # Execution role
        self.lambda_execution_role = self.create_service_role(
            "LambdaExecutionRole",
            ["lambda.amazonaws.com"],
            "Execution role for Biormika Lambda API",
            [
                iam.ManagedPolicy.from_aws_managed_policy_name(
                    "service-role/AWSLambdaBasicExecutionRole"
                )
            ],
        )

        # S3 permissions
        self.grant_s3_permissions(
            self.lambda_execution_role,
            f"arn:aws:s3:::{self.bucket_name}",
            S3_ACTIONS_READ_WRITE,
            ["*"],
        )

        # Log group
        self.log_group = self.create_log_group(
            "ApiLogGroup",
            LAMBDA_LOG_GROUP_NAME,
            getattr(
                logs.RetentionDays,
                f"{'ONE_WEEK' if LAMBDA_LOG_RETENTION_DAYS == 7 else 'ONE_MONTH'}",
            ),
        )

        # Build Lambda package
        self._build_lambda_package()

        # Lambda function
        self.api_function = _lambda.Function(
            self,
            "ApiFunction",
            runtime=getattr(_lambda.Runtime, LAMBDA_RUNTIME_PYTHON),
            architecture=getattr(_lambda.Architecture, LAMBDA_ARCHITECTURE),
            handler=LAMBDA_HANDLER,
            code=_lambda.Code.from_asset(self._get_lambda_package_path()),
            timeout=Duration.seconds(LAMBDA_TIMEOUT_SECONDS),
            memory_size=LAMBDA_MEMORY_SIZE_MB,
            role=self.lambda_execution_role,
            environment=self._get_lambda_environment(),
            log_group=self.log_group,
            description="Biormika EDF File Management API",
        )

    def _build_lambda_package(self) -> None:
        """Build Lambda deployment package if needed."""
        backend_dir = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../../Backend")
        )
        lambda_package_path = os.path.join(
            backend_dir, "lambda_deployment.zip")

        if not os.path.exists(lambda_package_path):
            print("Building Lambda deployment package...")
            subprocess.run(
                ["bash", "build_lambda.sh"],
                cwd=backend_dir,
                check=True,
            )

    def _get_lambda_package_path(self) -> str:
        """Get path to Lambda deployment package."""
        backend_dir = os.path.abspath(
            os.path.join(os.path.dirname(__file__), "../../Backend")
        )
        return os.path.join(backend_dir, "lambda_deployment.zip")

    def _get_lambda_environment(self) -> dict[str, str]:
        """Get Lambda environment variables."""
        return self.get_environment_variables(
            S3_BUCKET_NAME=self.bucket_name,
            ALLOWED_ORIGINS=",".join(self.allowed_origins),
            LAMBDA_ENVIRONMENT="true",
            S3_TRANSFER_ACCELERATION="true",
        )

    def _create_api_gateway(self) -> None:
        """Create API Gateway for Lambda function."""
        cors_options = create_api_cors_options(self.allowed_origins)

        self.api = apigw.LambdaRestApi(
            self,
            "ApiGateway",
            handler=self.api_function,
            proxy=True,
            description="Biormika EDF File Management API Gateway",
            deploy_options=apigw.StageOptions(
                stage_name=API_STAGE_NAME,
                throttling_rate_limit=API_THROTTLE_RATE_LIMIT,
                throttling_burst_limit=API_THROTTLE_BURST_LIMIT,
                metrics_enabled=True,
            ),
            default_cors_preflight_options=apigw.CorsOptions(**cors_options),
            endpoint_configuration=apigw.EndpointConfiguration(
                types=[apigw.EndpointType.REGIONAL]
            ),
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        self.create_output(
            "ApiGatewayUrl",
            self.api.url,
            "API Gateway URL for the Biormika API",
            "BiormikApiGatewayUrl",
        )

        self.create_output(
            "ApiGatewayId",
            self.api.rest_api_id,
            "API Gateway ID",
            "BiormikaApiGatewayId",
        )

        self.create_output(
            "LambdaFunctionArn",
            self.api_function.function_arn,
            "Lambda function ARN",
            "BiormikaLambdaFunctionArn",
        )
