#!/bin/bash

set -e

PROFILE="biormika"
REGION="us-east-1"

echo "🚀 Starting Biormika Frontend Deployment"
echo "========================================"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo "❌ AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check if AWS profile exists
if ! aws configure list-profiles | grep -q "$PROFILE"; then
    echo "❌ AWS profile '$PROFILE' not found. Please configure it first."
    exit 1
fi

# Get CDK outputs
echo "📊 Getting CDK outputs..."
CDK_OUTPUT_FILE="../Infra/cdk-outputs.json"

# Check if CDK outputs exist
if [ ! -f "$CDK_OUTPUT_FILE" ]; then
    echo "⚠️  CDK outputs not found. Deploying infrastructure first..."
    cd ../Infra
    cdk deploy --profile $PROFILE --outputs-file cdk-outputs.json
    cd ../Frontend
fi

# Read S3 bucket and CloudFront distribution from CDK outputs
S3_BUCKET_NAME=$(jq -r '.BiormikaStack.StaticSiteSiteBucketNameDAB52DD2' $CDK_OUTPUT_FILE)
CLOUDFRONT_DISTRIBUTION_ID=$(jq -r '.BiormikaStack.StaticSiteDistributionId8C64EF2A' $CDK_OUTPUT_FILE)
CLOUDFRONT_URL=$(jq -r '.BiormikaStack.StaticSiteCloudFrontURLC5D07089' $CDK_OUTPUT_FILE)

if [ "$S3_BUCKET_NAME" == "null" ] || [ "$CLOUDFRONT_DISTRIBUTION_ID" == "null" ]; then
    echo "❌ Could not find S3 bucket or CloudFront distribution in CDK outputs"
    echo "Please deploy the infrastructure first: cd ../Infra && cdk deploy --profile $PROFILE"
    exit 1
fi

echo "📦 S3 Bucket: $S3_BUCKET_NAME"
echo "☁️  CloudFront Distribution: $CLOUDFRONT_DISTRIBUTION_ID"
echo ""

# Build the production bundle
echo "🏗️  Building production bundle..."
npm run build:prod

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Build failed. 'dist' directory not found."
    exit 1
fi

# Deploy to S3
echo ""
echo "📤 Uploading to S3..."
aws s3 sync dist/ s3://$S3_BUCKET_NAME \
    --delete \
    --profile $PROFILE \
    --region $REGION \
    --cache-control "public, max-age=31536000" \
    --exclude "index.html" \
    --exclude "*.js.map"

# Upload index.html with no-cache headers
aws s3 cp dist/index.html s3://$S3_BUCKET_NAME/index.html \
    --profile $PROFILE \
    --region $REGION \
    --cache-control "public, max-age=0, must-revalidate" \
    --content-type "text/html"

# Invalidate CloudFront cache
echo ""
echo "🔄 Invalidating CloudFront cache..."
INVALIDATION_ID=$(aws cloudfront create-invalidation \
    --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
    --paths "/*" \
    --profile $PROFILE \
    --query "Invalidation.Id" \
    --output text)

echo "📋 Invalidation ID: $INVALIDATION_ID"

# Wait for invalidation to complete (optional)
echo ""
echo "⏳ Waiting for invalidation to complete..."
aws cloudfront wait invalidation-completed \
    --distribution-id $CLOUDFRONT_DISTRIBUTION_ID \
    --id $INVALIDATION_ID \
    --profile $PROFILE 2>/dev/null || true

echo ""
echo "✅ Deployment complete!"
echo "🌐 Your site is available at: $CLOUDFRONT_URL"
echo ""
echo "📝 Note: It may take a few minutes for changes to propagate globally."