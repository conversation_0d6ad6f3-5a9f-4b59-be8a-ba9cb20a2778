import React, { useEffect, useState } from "react";
import { Download, Trash2, FileIcon, RefreshCw, CheckSquare, Square, Settings } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { useFileActions } from "@/hooks/useFileActions";
import { formatFileSize, formatDate } from "@/utils/file";
import { useToast } from "@/hooks/useToast";
import { DataTable, type TableColumn } from "./tables/DataTable";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import { apiClient } from "@/services/api";
import type { FileInfo } from "@/types/api";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { EnhancedAnalysisParametersForm, type AnalysisParameters } from "./analysis/EnhancedAnalysisParametersForm";
import { FileDetailsStep } from "./analysis/FileDetailsStep";
import { AnalysisParametersForm } from "./analysis/AnalysisParametersForm";

interface FileManagerProps {
  refreshTrigger?: number;
}

export const FileManager: React.FC<FileManagerProps> = ({ refreshTrigger }) => {
  const { toast, showToast, hideToast } = useToast();
  const { dialogState, showConfirmDialog } = useConfirmDialog();
  const { files, loading, error, fetchFiles } = useFileOperations();
  const { handleDownload, handleDelete, isOperationInProgress } = useFileActions({
    onDeleteSuccess: fetchFiles,
    showConfirmDialog,
  });

  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [processing, setProcessing] = useState(false);
  const [parametersModalOpen, setParametersModalOpen] = useState(false);
  const [modalStep, setModalStep] = useState<"details" | "parameters">("details");
  const [useEnhancedForm, setUseEnhancedForm] = useState(false);
  const [fileMetadata, setFileMetadata] = useState<{
    channels: string[];
    samplingRate: number;
    duration: number;
  } | null>(null);

  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      setSelectedFiles([]);
    } else {
      setSelectedFiles(files.map((f) => f.key));
    }
  };

  const handleSelectFile = (fileKey: string) => {
    setSelectedFiles((prev) => (prev.includes(fileKey) ? prev.filter((k) => k !== fileKey) : [...prev, fileKey]));
  };

  const fetchFileMetadata = async (fileKey: string) => {
    try {
      // Fetch file metadata from backend
      const response = await apiClient.get(`/files/metadata/${encodeURIComponent(fileKey)}`);
      if (response.data) {
        setFileMetadata({
          channels: response.data.channels || [],
          samplingRate: response.data.sampling_rate || 256,
          duration: response.data.duration_seconds || 600,
        });
      }
    } catch (error) {
      console.error("Failed to fetch file metadata:", error);
      // Use default values if metadata fetch fails
      setFileMetadata({
        channels: [],
        samplingRate: 256,
        duration: 600,
      });
    }
  };

  const handleProcessFiles = async (enhanced = false) => {
    if (selectedFiles.length === 0) {
      showToast("Please select at least one file to process", "error");
      return;
    }

    // Fetch metadata for the first selected file if using enhanced form
    if (enhanced && selectedFiles.length > 0) {
      await fetchFileMetadata(selectedFiles[0]);
    }

    setUseEnhancedForm(enhanced);
    setModalStep(enhanced ? "details" : "parameters");
    setParametersModalOpen(true);
  };

  const handleSubmitWithParameters = async (params: AnalysisParameters) => {
    setParametersModalOpen(false);
    setModalStep("details");
    setProcessing(true);

    try {
      // Convert parameters to match API format
      const apiParams = {
        thresholds: {
          amplitude1: params.thresholds.amplitude_1,
          amplitude2: params.thresholds.amplitude_2,
          peaks1: params.thresholds.peaks_1,
          peaks2: params.thresholds.peaks_2,
          duration: params.thresholds.duration,
          temporal_sync: params.thresholds.temporal_sync,
          spatial_sync: params.thresholds.spatial_sync,
        },
        montage: params.montage,
        frequency: {
          low_cutoff: params.frequency.low_cutoff,
          high_cutoff: params.frequency.high_cutoff,
        },
        analysis_start: params.analysis_start,
        analysis_end: params.analysis_end,
      };

      await apiClient.post("/analysis/batch", {
        file_keys: selectedFiles,
        parameters: apiParams,
      });

      showToast(`Processing ${selectedFiles.length} file(s) with custom parameters. You'll receive an email when complete.`, "success");

      setSelectedFiles([]);
    } catch {
      showToast("Failed to submit files for processing", "error");
    } finally {
      setProcessing(false);
    }
  };

  const columns: TableColumn<FileInfo>[] = [
    {
      key: "select",
      header: (
        <button
          onClick={handleSelectAll}
          className="p-1 hover:bg-gray-100 rounded"
          title={selectedFiles.length === files.length ? "Deselect All" : "Select All"}
        >
          {selectedFiles.length === files.length ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
      accessor: (file) => (
        <button onClick={() => handleSelectFile(file.key)} className="p-1 hover:bg-gray-100 rounded">
          {selectedFiles.includes(file.key) ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
    },
    {
      key: "filename",
      header: "File Name",
      accessor: (file) => (
        <div className="flex items-center">
          <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">{file.filename}</span>
        </div>
      ),
    },
    {
      key: "size",
      header: "Size",
      accessor: (file) => <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>,
    },
    {
      key: "modified",
      header: "Modified",
      accessor: (file) => <span className="text-sm text-gray-500">{formatDate(file.last_modified)}</span>,
    },
    {
      key: "actions",
      header: "Actions",
      className: "text-right",
      accessor: (file) => (
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleDownload(file.key)}
            disabled={isOperationInProgress("download", file.key)}
            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
            title="Download"
          >
            {isOperationInProgress("download", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Download className="w-4 h-4" />}
          </button>
          <button
            onClick={() => handleDelete(file.key, file.filename)}
            disabled={isOperationInProgress("delete", file.key)}
            className="text-red-600 hover:text-red-900 disabled:opacity-50"
            title="Delete"
          >
            {isOperationInProgress("delete", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Trash2 className="w-4 h-4" />}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      {toast.isOpen && <Toast message={toast.message} type={toast.type} onClose={hideToast} />}
      <ConfirmDialog {...dialogState} />

      <div className="space-y-4">
        {/* Process Files Button */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckSquare className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">{selectedFiles.length} file(s) selected</span>
            </div>
            <div className="flex gap-2">
              {processing ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <button
                  onClick={() => handleProcessFiles(true)}
                  disabled={processing}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Configure & Analyze
                </button>
              )}
            </div>
          </div>
        )}

        <DataTable
          title={`Files Uploaded (Ready to be Analyzed) (${files.length})`}
          columns={columns}
          data={files}
          loading={loading}
          error={error}
          emptyMessage="No uploaded files available"
          onRefresh={fetchFiles}
          getRowKey={(file) => file.key}
        />
      </div>

      {/* Analysis Parameters Modal */}
      <Dialog
        open={parametersModalOpen}
        onOpenChange={(open) => {
          setParametersModalOpen(open);
          if (!open) setModalStep("details");
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              {modalStep === "details" ? "File Details Overview" : "Configure HFO Analysis Parameters"}
            </DialogTitle>
            <DialogDescription>
              {modalStep === "details"
                ? "Review file information before configuring analysis parameters"
                : useEnhancedForm
                ? "Configure comprehensive parameters matching your analysis requirements"
                : "Customize the parameters for detecting High-Frequency Oscillations (HFOs) in the selected files."}
            </DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            {modalStep === "details" && useEnhancedForm ? (
              <FileDetailsStep
                files={files.filter((f) => selectedFiles.includes(f.key))}
                onNext={() => setModalStep("parameters")}
                onCancel={() => {
                  setParametersModalOpen(false);
                  setModalStep("details");
                }}
              />
            ) : useEnhancedForm ? (
              <EnhancedAnalysisParametersForm
                onParametersChange={() => {}}
                onSubmit={handleSubmitWithParameters}
                isSubmitting={processing}
                channels={
                  fileMetadata?.channels || [
                    "FP1-F7",
                    "F7-T3",
                    "T3-T5",
                    "T5-O1",
                    "FP2-F8",
                    "F8-T4",
                    "T4-T6",
                    "T6-O2",
                    "FP1-F3",
                    "F3-C3",
                    "C3-P3",
                    "P3-O1",
                    "FP2-F4",
                    "F4-C4",
                    "C4-P4",
                    "P4-O2",
                  ]
                }
                fileInfo={{
                  filename: files.filter((f) => selectedFiles.includes(f.key))[0]?.filename || "",
                  samplingRate: fileMetadata?.samplingRate || 256,
                  duration: fileMetadata?.duration || 600,
                }}
              />
            ) : (
              <AnalysisParametersForm onParametersChange={() => {}} onSubmit={handleSubmitWithParameters} isSubmitting={processing} channels={[]} />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
