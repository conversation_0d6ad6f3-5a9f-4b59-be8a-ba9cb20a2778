import React from "react";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";

interface StateRendererProps {
  loading?: boolean;
  error?: string | null;
  empty?: boolean;
  emptyMessage?: string;
  onRetry?: () => void;
  children: React.ReactNode;
}

export const StateRenderer: React.FC<StateRendererProps> = ({
  loading = false,
  error = null,
  empty = false,
  emptyMessage = "No data available",
  onRetry,
  children,
}) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600 mb-4">{error}</p>
        {onRetry && (
          <Button onClick={onRetry} variant="outline">
            Retry
          </Button>
        )}
      </div>
    );
  }

  if (empty) {
    return (
      <div className="text-center py-8 text-gray-500">
        {emptyMessage}
      </div>
    );
  }

  return <>{children}</>;
};