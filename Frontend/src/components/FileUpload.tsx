import React, { useState, useEffect } from "react";
import { useDragAndDrop } from "@/hooks/useDragAndDrop";
import { useFileUpload } from "@/hooks/useFileUpload";
import { DropZone } from "./upload/DropZone";
import { FilePreview } from "./upload/FilePreview";
import { ErrorMessage } from "./upload/ErrorMessage";
import { FILE_CONFIG } from "@/constants";

interface FileUploadProps {
  onUploadComplete?: () => void;
}

export const FileUpload: React.FC<FileUploadProps> = ({ onUploadComplete }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState<string>("");
  
  const { isDragging, dragHandlers } = useDragAndDrop({
    onDrop: (files: File[]) => {
      if (files.length > 0) {
        handleFileSelect(files[0]);
      }
    },
    accept: FILE_CONFIG.ALLOWED_EXTENSIONS,
    multiple: false
  });

  const { uploadFile, isUploading, uploadProgress, validateFiles } = useFileUpload({
    onSuccess: () => {
      setUploadStatus("success");
      setTimeout(() => {
        resetUpload();
        onUploadComplete?.();
      }, FILE_CONFIG.UPLOAD_SUCCESS_DELAY_MS);
    },
    onError: (error) => {
      setUploadStatus("error");
      setErrorMessage(error.message);
    }
  });

  useEffect(() => {
    if (isUploading) {
      setUploadStatus("uploading");
    }
  }, [isUploading]);

  const handleFileSelect = (file: File) => {
    const { errors } = validateFiles([file]);
    
    if (errors.length > 0) {
      setErrorMessage(errors[0]);
      setUploadStatus("error");
      return;
    }

    setSelectedFile(file);
    setErrorMessage("");
    setUploadStatus("idle");
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;
    await uploadFile([selectedFile]);
  };

  const resetUpload = () => {
    setSelectedFile(null);
    setUploadStatus("idle");
    setErrorMessage("");
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300 hover:border-gray-400"
        }`}
        {...dragHandlers}
      >
        <input 
          type="file" 
          id="file-input" 
          className="hidden" 
          accept=".edf,.EDF" 
          onChange={handleFileInputChange} 
        />

        {!selectedFile ? (
          <DropZone inputId="file-input" />
        ) : (
          <FilePreview
            file={selectedFile}
            uploadStatus={uploadStatus}
            uploadProgress={uploadProgress}
            onUpload={handleUpload}
            onCancel={resetUpload}
          />
        )}

        {uploadStatus === "error" && errorMessage && (
          <ErrorMessage message={errorMessage} />
        )}
      </div>
    </div>
  );
};
