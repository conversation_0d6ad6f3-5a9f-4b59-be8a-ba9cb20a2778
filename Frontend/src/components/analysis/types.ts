export interface HFOEvent {
  channel: string;
  start_time: number;
  end_time: number;
  peak_frequency: number;
  amplitude: number;
}

export interface AnalysisMetadata {
  filename: string;
  sampling_rate: number;
  duration_seconds: number;
  channels: string[];
  processing_time: number;
}

export interface AnalysisStatistics {
  total_hfos: number;
  hfo_density: number;
  channels_with_hfos: string[];
  hfo_rate_per_channel?: Record<string, number>;
}

export interface AnalysisResults {
  metadata: AnalysisMetadata;
  statistics: AnalysisStatistics;
  channel_data: Record<string, number[]>;
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}