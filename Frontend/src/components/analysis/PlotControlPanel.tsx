import React from 'react';
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Eye,
  EyeOff,
  Settings,
  Download,
  Maximize2,
  ChevronLeft,
  ChevronRight,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface PlotControlPanelProps {
  // Time window controls
  timeWindow: [number, number];
  onTimeWindowChange: (window: [number, number]) => void;
  duration: number;

  // Zoom controls
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;

  // Display controls
  gain: number;
  onGainChange: (gain: number) => void;
  showHFOMarkers: boolean;
  onToggleHFOMarkers: () => void;
  showThresholds: boolean;
  onToggleThresholds: () => void;

  // Navigation
  onNavigate?: (direction: 'prev' | 'next') => void;

  // Export
  onExport?: () => void;

  // Fullscreen
  onFullscreen?: () => void;
  isFullscreen?: boolean;

  // Optional keyboard shortcuts display
  showKeyboardHints?: boolean;
}

export const PlotControlPanel: React.FC<PlotControlPanelProps> = ({
  timeWindow,
  onTimeWindowChange,
  duration,
  onZoomIn,
  onZoomOut,
  onReset,
  gain,
  onGainChange,
  showHFOMarkers,
  onToggleHFOMarkers,
  showThresholds,
  onToggleThresholds,
  onNavigate,
  onExport,
  onFullscreen,
  isFullscreen = false,
  showKeyboardHints = true
}) => {
  // Preset time window values
  const timeWindowPresets = [1, 5, 10, 30];
  const windowSize = timeWindow[1] - timeWindow[0];

  const handleTimeWindowPreset = (preset: number) => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newStart = Math.max(0, center - preset / 2);
    const newEnd = Math.min(duration, center + preset / 2);

    // Adjust if we hit boundaries
    if (newEnd - newStart < preset) {
      if (newStart === 0) {
        onTimeWindowChange([0, Math.min(preset, duration)]);
      } else {
        onTimeWindowChange([Math.max(0, duration - preset), duration]);
      }
    } else {
      onTimeWindowChange([newStart, newEnd]);
    }
  };

  const handleSliderChange = (value: number[]) => {
    const newStart = value[0];
    const windowSize = timeWindow[1] - timeWindow[0];
    onTimeWindowChange([newStart, Math.min(newStart + windowSize, duration)]);
  };

  const canNavigatePrev = timeWindow[0] > 0;
  const canNavigateNext = timeWindow[1] < duration;

  return (
    <TooltipProvider>
      <Card className="p-4 space-y-4">
        {/* Main controls row */}
        <div className="flex items-center justify-between gap-4">
          {/* Zoom controls */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 border rounded-md p-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onZoomIn}
                    className="h-8 w-8 p-0"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom In {showKeyboardHints && <span className="text-xs">(A)</span>}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onZoomOut}
                    className="h-8 w-8 p-0"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom Out {showKeyboardHints && <span className="text-xs">(D)</span>}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onReset}
                    className="h-8 w-8 p-0"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Reset View</p>
                </TooltipContent>
              </Tooltip>
            </div>

            {/* Navigation controls */}
            {onNavigate && (
              <div className="flex items-center gap-1 border rounded-md p-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onNavigate('prev')}
                      disabled={!canNavigatePrev}
                      className="h-8 w-8 p-0"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Previous {showKeyboardHints && <span className="text-xs">(O)</span>}</p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onNavigate('next')}
                      disabled={!canNavigateNext}
                      className="h-8 w-8 p-0"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Next {showKeyboardHints && <span className="text-xs">(P)</span>}</p>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>

          {/* Time window presets */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">Window:</span>
            <div className="flex gap-1">
              {timeWindowPresets.map((preset) => (
                <Button
                  key={preset}
                  variant={Math.abs(windowSize - preset) < 0.1 ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleTimeWindowPreset(preset)}
                  className="h-8 px-2"
                >
                  {preset}s
                </Button>
              ))}
            </div>
          </div>

          {/* Gain control */}
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-gray-600" />
            <span className="text-sm text-gray-600">Gain:</span>
            <Select
              value={gain.toString()}
              onValueChange={(value: string) => onGainChange(Number(value))}
            >
              <SelectTrigger className="w-24 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 μV/div</SelectItem>
                <SelectItem value="10">10 μV/div</SelectItem>
                <SelectItem value="20">20 μV/div</SelectItem>
                <SelectItem value="50">50 μV/div</SelectItem>
                <SelectItem value="100">100 μV/div</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Toggle controls */}
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={showHFOMarkers ? "default" : "outline"}
                  size="sm"
                  onClick={onToggleHFOMarkers}
                  className="h-8"
                >
                  {showHFOMarkers ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  <span className="ml-1">HFOs</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{showHFOMarkers ? 'Hide' : 'Show'} HFO Markers</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={showThresholds ? "default" : "outline"}
                  size="sm"
                  onClick={onToggleThresholds}
                  className="h-8"
                >
                  <Settings className="h-4 w-4" />
                  <span className="ml-1">Thresholds</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{showThresholds ? 'Hide' : 'Show'} Detection Thresholds</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {onExport && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onExport}
                    className="h-8"
                  >
                    <Download className="h-4 w-4" />
                    <span className="ml-1">Export</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Export plot as image</p>
                </TooltipContent>
              </Tooltip>
            )}

            {onFullscreen && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onFullscreen}
                    className="h-8"
                  >
                    <Maximize2 className="h-4 w-4" />
                    <span className="ml-1">{isFullscreen ? 'Exit' : 'Fullscreen'}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Toggle Fullscreen {showKeyboardHints && <span className="text-xs">(F)</span>}</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>

        {/* Time slider */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Time Range:</span>
            <span className="font-mono font-medium">
              {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
            </span>
          </div>
          <div className="flex items-center gap-4">
            <span className="text-xs text-gray-500 w-10">0s</span>
            <Slider
              value={[timeWindow[0]]}
              onValueChange={handleSliderChange}
              max={Math.max(0, duration - windowSize)}
              step={0.1}
              className="flex-1"
            />
            <span className="text-xs text-gray-500 w-10">{duration.toFixed(0)}s</span>
          </div>

          {/* Position indicator */}
          <div className="relative h-2 bg-gray-200 rounded">
            <div
              className="absolute h-full bg-blue-600 rounded transition-all duration-200"
              style={{
                left: `${(timeWindow[0] / duration) * 100}%`,
                width: `${((timeWindow[1] - timeWindow[0]) / duration) * 100}%`,
              }}
            />
          </div>
        </div>

        {/* Keyboard shortcuts hint */}
        {showKeyboardHints && (
          <div className="text-xs text-gray-500 flex items-center justify-center gap-4 pt-2 border-t">
            <span>Keyboard Shortcuts:</span>
            <span>A/D - Zoom</span>
            <span>O/P - Navigate</span>
            <span>F - Fullscreen</span>
            <span>Shift+Scroll - Horizontal scroll</span>
          </div>
        )}
      </Card>
    </TooltipProvider>
  );
};