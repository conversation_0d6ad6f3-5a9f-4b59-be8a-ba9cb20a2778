import React from "react";
import type { HFOEvent } from '@/types/hfo';
import { HFO_TYPE_COLORS, HFO_TYPE_LABELS } from '@/types/hfo';

export type { HFOEvent };

interface HfoEventsTableProps {
  events: HFOEvent[];
  maxDisplay?: number;
}

export const HfoEventsTable: React.FC<HfoEventsTableProps> = ({
  events,
  maxDisplay = 50
}) => {
  const displayEvents = events.slice(0, maxDisplay);

  return (
    <div className="border rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-3">HFO Events Details</h3>
      <div className="overflow-x-auto max-h-64 overflow-y-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50 sticky top-0">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Channel
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Start Time
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Duration
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Peak Freq
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                Amplitude
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {displayEvents.map((hfo, idx) => (
              <tr key={idx} className="hover:bg-gray-50">
                <td className="px-4 py-2 whitespace-nowrap">
                  <span
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                    style={{
                      backgroundColor: `${HFO_TYPE_COLORS[hfo.type || 'accepted']}20`,
                      color: HFO_TYPE_COLORS[hfo.type || 'accepted']
                    }}
                  >
                    {hfo.type === 'accepted' ? '✓' : '✗'} {HFO_TYPE_LABELS[hfo.type || 'accepted']}
                  </span>
                </td>
                <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-900">
                  {hfo.channel}
                </td>
                <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                  {hfo.start_time.toFixed(3)}s
                </td>
                <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                  {((hfo.end_time - hfo.start_time) * 1000).toFixed(1)}ms
                </td>
                <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                  {hfo.peak_frequency}Hz
                </td>
                <td className="px-6 py-2 whitespace-nowrap text-sm text-gray-500">
                  {hfo.amplitude.toFixed(1)}μV
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {events.length > maxDisplay && (
          <p className="text-sm text-gray-500 mt-2 px-6">
            Showing {maxDisplay} of {events.length} events
          </p>
        )}
      </div>
    </div>
  );
};