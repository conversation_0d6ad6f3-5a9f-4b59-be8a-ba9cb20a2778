import React, { useEffect, useState, useCallback, useRef, useMemo } from "react";
import { Download } from "lucide-react";
import { apiClient } from "@/services/api";
import type { HFOEvent, HFOType } from "@/types/hfo";
import { ChannelGrid } from "./ChannelGrid";
import { ChannelSelector } from "./ChannelSelector";
import { TimeNavigationControls } from "./TimeNavigationControls";

interface AnalysisResults {
  metadata: {
    filename: string;
    sampling_rate: number;
    duration_seconds: number;
    channels: string[];
    processing_time: number;
    montage?: string;
    low_cutoff?: number;
    high_cutoff?: number;
  };
  statistics: {
    total_hfos: number;
    hfo_density: number;
    channels_with_hfos: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  channel_data: Record<string, number[]>;
  sampling_info?: {
    downsampled: boolean;
    effective_sampling_rate: number;
    original_sampling_rate: number;
    duration_seconds: number;
    downsample_factor?: number;
  };
  hfo_events: HFOEvent[];
  download_urls?: {
    results_json: string;
    hfo_events_csv: string;
  };
}

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewer: React.FC<ResultsViewerProps> = ({ jobId, onClose }) => {
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [timeWindow, setTimeWindow] = useState<[number, number]>([0, 10]);
  const [timeWindowSize, setTimeWindowSize] = useState(10);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [visibleHFOTypes] = useState<HFOType[]>(["accepted", "rejected", "rejected_long", "lfo_rejected", "noise_rejected"]);
  const [currentChunk, setCurrentChunk] = useState(0);
  const channelViewRef = useRef<HTMLDivElement>(null);

  const fetchResults = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiClient.get<AnalysisResults>(`/analysis/results/${jobId}`);

      if (!response.data || !response.data.metadata) {
        throw new Error("Invalid results format");
      }

      // Analyze HFO time distribution
      if (response.data.hfo_events && response.data.hfo_events.length > 0) {
        const timeDistribution: Record<string, number> = {};
        const windowSize = 10; // 10-second windows
        const totalDuration = response.data.metadata.duration_seconds || 60;

        // Initialize time windows
        for (let i = 0; i < totalDuration; i += windowSize) {
          const windowKey = `${i}-${i + windowSize}s`;
          timeDistribution[windowKey] = 0;
        }

        // Count HFOs per time window
        response.data.hfo_events.forEach((hfo) => {
          const windowStart = Math.floor(hfo.start_time / windowSize) * windowSize;
          const windowKey = `${windowStart}-${windowStart + windowSize}s`;
          if (timeDistribution[windowKey] !== undefined) {
            timeDistribution[windowKey]++;
          }
        });

        // Analyze HFO types
        const typeCounts: Record<string, number> = {};
        response.data.hfo_events.forEach((event: HFOEvent) => {
          const eventType = event.type || "undefined";
          typeCounts[eventType] = (typeCounts[eventType] || 0) + 1;
        });
      }

      setResults(response.data);

      if (response.data.metadata.channels && response.data.metadata.channels.length > 0) {
        setSelectedChannels(response.data.metadata.channels);
      }
      setLoading(false);
    } catch (err) {
      const error = err as { response?: { data?: { detail?: string } } };
      setError(error.response?.data?.detail || "Failed to load results");
      setLoading(false);
    }
  }, [jobId]);

  useEffect(() => {
    fetchResults();
  }, [fetchResults]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!results || e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (e.key.toLowerCase()) {
        case "a": // Zoom in
          setTimeWindowSize((prev) => {
            const newSize = Math.max(1, prev / 2);
            const center = (timeWindow[0] + timeWindow[1]) / 2;
            const newStart = Math.max(0, Math.min(results.metadata.duration_seconds - newSize, center - newSize / 2));
            setTimeWindow([newStart, newStart + newSize]);
            return newSize;
          });
          break;
        case "d": // Zoom out
          setTimeWindowSize((prev) => {
            const newSize = Math.min(60, prev * 2);
            const center = (timeWindow[0] + timeWindow[1]) / 2;
            const newStart = Math.max(0, Math.min(results.metadata.duration_seconds - newSize, center - newSize / 2));
            setTimeWindow([newStart, newStart + newSize]);
            return newSize;
          });
          break;
        case "o": // Previous chunk
          if (currentChunk > 0) {
            const newChunk = currentChunk - 1;
            setCurrentChunk(newChunk);
            const newStart = newChunk * 10;
            setTimeWindow([newStart, Math.min(newStart + timeWindowSize, results.metadata.duration_seconds)]);
          }
          break;
        case "p": {
          // Next chunk
          const totalChunks = Math.ceil(results.metadata.duration_seconds / 10);
          if (currentChunk < totalChunks - 1) {
            const newChunk = currentChunk + 1;
            setCurrentChunk(newChunk);
            const newStart = newChunk * 10;
            setTimeWindow([newStart, Math.min(newStart + timeWindowSize, results.metadata.duration_seconds)]);
          }
          break;
        }
        case "f": // Fullscreen
          if (channelViewRef.current) {
            if (!document.fullscreenElement) {
              channelViewRef.current.requestFullscreen();
            } else {
              document.exitFullscreen();
            }
          }
          break;
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => document.removeEventListener("keydown", handleKeyPress);
  }, [results, timeWindow, timeWindowSize, currentChunk]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () => document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  // Calculate HFO statistics by type
  const hfoStatsByType = useMemo(() => {
    if (!results) return null;

    const stats = {
      total: results.hfo_events.length,
      accepted: results.hfo_events.filter((e) => (e.type || "accepted") === "accepted").length,
      rejected: results.hfo_events.filter((e) => e.type === "rejected").length,
      rejected_long: results.hfo_events.filter((e) => e.type === "rejected_long").length,
      lfo_rejected: results.hfo_events.filter((e) => e.type === "lfo_rejected").length,
      noise_rejected: results.hfo_events.filter((e) => e.type === "noise_rejected").length,
    };
    return stats;
  }, [results]);

  // Filter HFO events by visible types
  const filteredHfoEvents = useMemo(() => {
    if (!results) return [];
    return results.hfo_events.filter((event) => {
      const eventType = event.type || "accepted";
      return visibleHFOTypes.includes(eventType);
    });
  }, [results, visibleHFOTypes]);

  const downloadResults = async (format: string) => {
    try {
      // For comprehensive report
      if (format === "report") {
        const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=report`);
        if (response.data && response.data.download_url) {
          window.open(response.data.download_url, "_blank");
        }
        return;
      }

      const response = await apiClient.get<{ download_url: string }>(`/analysis/download/${jobId}?format=${format}`);

      if (response.data && response.data.download_url) {
        window.open(response.data.download_url, "_blank");
      } else {
        handleLocalDownload(format);
      }
    } catch {
      handleLocalDownload(format);
    }
  };

  const handleLocalDownload = (format: string) => {
    if (!results) return;

    const data = format === "json" ? JSON.stringify(results, null, 2) : convertToCSV(results.hfo_events);
    const blob = new Blob([data], {
      type: format === "json" ? "application/json" : "text/csv",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `analysis_results_${jobId}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const convertToCSV = (events: HFOEvent[]) => {
    const headers = ["Channel", "Start Time", "End Time", "Peak Frequency", "Amplitude"];
    const rows = events.map((e) => [e.channel, e.start_time, e.end_time, e.peak_frequency, e.amplitude]);
    return [headers, ...rows].map((row) => row.join(",")).join("\n");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading analysis results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg">
        <p className="text-red-800">{error}</p>
      </div>
    );
  }

  if (!results) return null;

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-gray-900">HFO Analysis Results</h1>
            <p className="text-sm text-gray-600">{results.metadata.filename}</p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => downloadResults("csv")}
              className="px-3 py-1 bg-black text-white text-xs rounded hover:bg-gray-800 transition-colors flex items-center gap-1"
              title="Download HFO events"
            >
              <Download className="w-3 h-3" />
              Export
            </button>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700 text-2xl"
                title="Close"
              >
                ×
              </button>
            )}
          </div>
        </div>
      </header>

      {/* Main Graph View */}
      <div className="flex-1 flex flex-col overflow-hidden">

        {/* Time Navigation Controls */}
        <div className="px-4 pt-4">
          <TimeNavigationControls
          timeWindow={timeWindow}
          timeWindowSize={timeWindowSize}
          totalDuration={results.metadata.duration_seconds}
          currentChunk={currentChunk}
          totalChunks={Math.ceil(results.metadata.duration_seconds / 10)}
          onTimeWindowChange={setTimeWindow}
          onTimeWindowSizeChange={setTimeWindowSize}
          onReset={() => {
            setTimeWindow([0, 10]);
            setTimeWindowSize(10);
            setCurrentChunk(0);
            setSelectedChannels(results.metadata.channels);
          }}
          onFullscreen={() => {
            if (channelViewRef.current) {
              if (!document.fullscreenElement) {
                channelViewRef.current.requestFullscreen();
              } else {
                document.exitFullscreen();
              }
            }
          }}
          />

          {/* Position indicator */}
          <div className="mt-2 bg-gray-100 rounded p-2">
            <div className="relative h-2 bg-gray-300 rounded">
              <div
                className="absolute h-full bg-black rounded transition-all duration-300"
                style={{
                  left: `${(timeWindow[0] / results.metadata.duration_seconds) * 100}%`,
                  width: `${((timeWindow[1] - timeWindow[0]) / results.metadata.duration_seconds) * 100}%`,
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-600 mt-1">
              <span>0s</span>
              <span className="font-semibold">
                {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
              </span>
              <span>{results.metadata.duration_seconds.toFixed(1)}s</span>
            </div>
          </div>
        </div>

        {/* Multi-Channel View */}
        <div
          ref={channelViewRef}
          className={`flex-1 flex overflow-hidden bg-white rounded-lg shadow-sm ${isFullscreen ? 'm-0' : 'm-4'}`}
        >
          {/* Channel Selector Sidebar */}
          {!isFullscreen && (
            <div className="flex-shrink-0 w-64 bg-gray-50 border-r border-gray-200 overflow-hidden flex flex-col">
              <ChannelSelector
                channels={results.metadata.channels}
                selectedChannels={selectedChannels}
                onChannelToggle={(channel) => {
                  setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
                }}
                onSelectAll={() => setSelectedChannels(results.metadata.channels)}
                onClearAll={() => setSelectedChannels([])}
                hfoCountByChannel={results.metadata.channels.reduce((acc, channel) => {
                  acc[channel] = results.hfo_events.filter((h) => h.channel === channel).length;
                  return acc;
                }, {} as Record<string, number>)}
              />
            </div>
          )}

          {/* Channel Grid Display */}
          <div className="flex-1 flex flex-col">
            <div className="flex-shrink-0 h-14 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-sm font-semibold text-gray-900">Channel View</h2>
                <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                  {selectedChannels.length}/{results.metadata.channels.length} channels
                </span>
                {hfoStatsByType && (
                  <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                    {filteredHfoEvents.length} of {hfoStatsByType.total} HFOs
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {isFullscreen && (
                  <button
                    onClick={() => document.exitFullscreen()}
                    className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                  >
                    Exit Fullscreen (F)
                  </button>
                )}
              </div>
            </div>

            <div className="flex-1 overflow-auto">
              <ChannelGrid
                channelData={results.channel_data}
                visibleChannels={selectedChannels}
                timeWindow={timeWindow}
                samplingRate={results.sampling_info?.effective_sampling_rate || results.metadata.sampling_rate}
                hfoEvents={filteredHfoEvents}
                showHFOMarkers={true}
                samplingInfo={results.sampling_info}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
