import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileIcon, Clock, Activity, Database } from "lucide-react";
import { formatFileSize, formatDate } from "@/utils/file";

interface FileDetailsStepProps {
  files: Array<{
    key: string;
    filename: string;
    size: number;
    last_modified: string;
  }>;
  fileMetadata?: {
    samplingRate?: number;
    duration?: number;
    channels?: string[];
    startDate?: string;
    startTime?: string;
    endDate?: string;
    endTime?: string;
  };
  onNext: () => void;
  onCancel: () => void;
}

export const FileDetailsStep: React.FC<FileDetailsStepProps> = ({
  files,
  fileMetadata,
  onNext,
  onCancel,
}) => {
  const formatDuration = (seconds: number | undefined) => {
    if (!seconds) return "N/A";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-semibold text-gray-800">File Details Overview</h2>
        <p className="text-gray-600 mt-2">
          Review the technical details extracted from your uploaded EDF file{files.length > 1 ? 's' : ''}.
        </p>
      </div>

      {/* File Information Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileIcon className="h-5 w-5" />
            Selected Files
          </CardTitle>
          <CardDescription>
            {files.length} file{files.length > 1 ? 's' : ''} selected for analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Filename
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Modified
                  </th>
                  {fileMetadata && (
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Frequency (Hz)
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {files.map((file) => (
                  <tr key={file.key} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <span className="text-sm font-medium text-gray-900">
                          {file.filename}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                      {formatFileSize(file.size)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                      {formatDate(file.last_modified)}
                    </td>
                    {fileMetadata && (
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600">
                        {fileMetadata.samplingRate || "N/A"}
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Technical Metadata */}
      {fileMetadata && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Recording Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Clock className="h-5 w-5" />
                Recording Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Start Date/Time:</span>
                <span className="text-sm font-medium">
                  {fileMetadata.startDate && fileMetadata.startTime
                    ? `${fileMetadata.startDate} ${fileMetadata.startTime}`
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">End Date/Time:</span>
                <span className="text-sm font-medium">
                  {fileMetadata.endDate && fileMetadata.endTime
                    ? `${fileMetadata.endDate} ${fileMetadata.endTime}`
                    : "N/A"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Duration:</span>
                <span className="text-sm font-medium">
                  {formatDuration(fileMetadata.duration)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Sampling Rate:</span>
                <span className="text-sm font-medium">
                  {fileMetadata.samplingRate ? `${fileMetadata.samplingRate} Hz` : "N/A"}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Channel Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Activity className="h-5 w-5" />
                Channel Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Total Channels:</span>
                  <span className="text-sm font-medium">
                    {fileMetadata.channels ? fileMetadata.channels.length : 0}
                  </span>
                </div>
                {fileMetadata.channels && fileMetadata.channels.length > 0 && (
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Available Channels:</p>
                    <div className="max-h-32 overflow-y-auto">
                      <div className="grid grid-cols-3 gap-1">
                        {fileMetadata.channels.slice(0, 15).map((channel, idx) => (
                          <span key={idx} className="text-xs bg-gray-100 px-2 py-1 rounded">
                            {channel}
                          </span>
                        ))}
                        {fileMetadata.channels.length > 15 && (
                          <span className="text-xs text-gray-500 italic">
                            +{fileMetadata.channels.length - 15} more...
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* File Validation Status */}
      <Card className="border-green-200 bg-green-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg text-green-800">
            <Database className="h-5 w-5" />
            File Validation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <p className="text-sm font-medium text-green-800">
                ✓ File format validated successfully
              </p>
              <p className="text-xs text-green-600">
                The EDF file structure has been verified and is ready for analysis
              </p>
            </div>
            <div className="text-green-600">
              <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between pt-4">
        <Button
          variant="outline"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          onClick={onNext}
          size="lg"
          className="px-8"
        >
          Configure Analysis Parameters →
        </Button>
      </div>
    </div>
  );
};