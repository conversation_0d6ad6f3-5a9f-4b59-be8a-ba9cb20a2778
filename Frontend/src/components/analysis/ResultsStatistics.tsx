import React from "react";
import { Activity, BarChart3, FileText, Clock } from "lucide-react";

interface ResultsStatisticsProps {
  totalHfos: number;
  hfoDensity: number;
  channelCount: number;
  durationSeconds: number;
}

export const ResultsStatistics: React.FC<ResultsStatisticsProps> = ({
  totalHfos,
  hfoDensity,
  channelCount,
  durationSeconds
}) => {
  return (
    <div className="grid grid-cols-4 gap-4">
      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-center text-blue-600 mb-2">
          <Activity className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">Total HFOs</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{totalHfos}</p>
      </div>

      <div className="bg-green-50 p-4 rounded-lg">
        <div className="flex items-center text-green-600 mb-2">
          <BarChart3 className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">HFO Density</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">
          {hfoDensity.toFixed(2)}/min
        </p>
      </div>

      <div className="bg-purple-50 p-4 rounded-lg">
        <div className="flex items-center text-purple-600 mb-2">
          <FileText className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">Channels</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{channelCount}</p>
      </div>

      <div className="bg-orange-50 p-4 rounded-lg">
        <div className="flex items-center text-orange-600 mb-2">
          <Clock className="w-5 h-5 mr-2" />
          <span className="text-sm font-medium">Duration</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">
          {Math.round(durationSeconds / 60)} min
        </p>
      </div>
    </div>
  );
};