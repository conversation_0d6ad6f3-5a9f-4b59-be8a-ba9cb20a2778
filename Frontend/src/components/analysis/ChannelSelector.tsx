import React from 'react';
import { Layers } from 'lucide-react';

interface ChannelSelectorProps {
  channels: string[];
  selectedChannels: string[];
  onChannelToggle: (channel: string) => void;
  onSelectAll: () => void;
  onClearAll: () => void;
  hfoCountByChannel?: Record<string, number>;
}

export const ChannelSelector: React.FC<ChannelSelectorProps> = ({
  channels,
  selectedChannels,
  onChannelToggle,
  onSelectAll,
  onClearAll,
  hfoCountByChannel = {},
}) => {
  const totalHfos = Object.values(hfoCountByChannel).reduce((sum, count) => sum + count, 0);
  const channelsWithHfos = Object.keys(hfoCountByChannel).filter(ch => hfoCountByChannel[ch] > 0).length;

  return (
    <div className="flex-shrink-0 w-64 bg-gray-50 border-r border-gray-200 overflow-hidden flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <h3 className="text-xs font-semibold text-gray-900 flex items-center gap-2">
          <Layers className="w-3 h-3 text-gray-600" />
          Channel Selection
        </h3>
      </div>

      {/* Control Buttons */}
      <div className="p-3 border-b border-gray-200">
        <button
          onClick={onSelectAll}
          className="w-full text-xs text-gray-700 hover:text-black font-medium"
        >
          Select All
        </button>
        <button
          onClick={onClearAll}
          className="w-full text-xs text-gray-600 hover:text-gray-700 font-medium mt-1"
        >
          Clear Selection
        </button>
      </div>

      {/* HFO Summary */}
      {totalHfos > 0 && (
        <div className="p-3 bg-blue-50 border-b border-blue-200">
          <div className="text-xs font-medium text-blue-900">HFO Summary</div>
          <div className="mt-1 text-xs text-blue-700">
            {totalHfos} HFOs in {channelsWithHfos} channel{channelsWithHfos !== 1 ? 's' : ''}
          </div>
        </div>
      )}

      {/* Channel List */}
      <div className="flex-1 overflow-y-auto p-3">
        <div className="space-y-1">
          {channels.map(channel => {
            const isSelected = selectedChannels.includes(channel);
            const hfoCount = hfoCountByChannel[channel] || 0;

            return (
              <label
                key={channel}
                className={`
                  flex items-center gap-2 p-1.5 rounded cursor-pointer transition-all
                  hover:bg-white
                  ${isSelected && 'bg-white border border-gray-300'}
                `}
              >
                <input
                  type="checkbox"
                  checked={isSelected}
                  onChange={() => onChannelToggle(channel)}
                  className="w-3 h-3 text-gray-700 border-gray-300 rounded focus:ring-gray-500"
                />
                <div className="flex-1">
                  <span className="text-xs font-medium text-gray-700">{channel}</span>
                  <div
                    className="h-1 mt-1 rounded-full"
                    style={{
                      background: isSelected ? '#000000' : '#d1d5db',
                    }}
                  />
                </div>
                {hfoCount > 0 && (
                  <span className="text-xs text-red-600 font-medium">
                    {hfoCount}
                  </span>
                )}
              </label>
            );
          })}
        </div>
      </div>

      {/* Footer Info */}
      <div className="p-3 border-t border-gray-200 bg-white">
        <div className="text-xs text-gray-500">
          <div className="flex items-center gap-1 mb-1">
            <div className="w-2 h-2 bg-red-500 rounded-full" />
            <span>Channels with HFO events</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full" />
            <span>No HFO events detected</span>
          </div>
        </div>
      </div>
    </div>
  );
};