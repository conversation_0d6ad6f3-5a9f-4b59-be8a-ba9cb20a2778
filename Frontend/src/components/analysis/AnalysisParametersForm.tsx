import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Settings, Info } from "lucide-react";

export interface AnalysisParameters {
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  thresholds: {
    amplitude_1: number; // StdDev above energy signal
    amplitude_2: number; // StdDev above baseline
    peaks_1: number; // Min peaks in HFO
    peaks_2: number; // Min peaks above threshold
    duration: number; // Min HFO length (ms)
    temporal_sync: number; // Min separation (ms)
    spatial_sync: number; // Channel delay (ms)
  };
  montage: {
    type: "bipolar" | "average" | "referential";
    reference?: string;
  };
  analysis_start?: number;
  analysis_end?: number;
}

interface AnalysisParametersFormProps {
  onParametersChange: (params: AnalysisParameters) => void;
  onSubmit: (params: AnalysisParameters) => void;
  isSubmitting?: boolean;
  channels?: string[];
}

const inputClass = "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent";
const labelClass = "flex items-center gap-2 text-sm font-medium";

export const AnalysisParametersForm: React.FC<AnalysisParametersFormProps> = ({
  onParametersChange,
  onSubmit,
  isSubmitting = false,
  channels = [],
}) => {
  // Default parameters (aligned with original backend)
  const [parameters, setParameters] = useState<AnalysisParameters>({
    frequency: {
      low_cutoff: 50,
      high_cutoff: 300,
    },
    thresholds: {
      amplitude_1: 2,
      amplitude_2: 2,
      peaks_1: 6, // Min peaks
      peaks_2: 3, // Min peaks above baseline
      duration: 10, // Min duration (ms)
      temporal_sync: 10, // Min separation (ms)
      spatial_sync: 10, // Channel delay (ms) across channels in original
    },
    montage: {
      type: "bipolar",
    },
    analysis_start: undefined,
    analysis_end: undefined,
  });

  const updateParameter = (section: string, field: string, value: string | number) => {
    // Handle top-level optional fields
    if (section === "root") {
      const updated = {
        ...parameters,
        [field]: value === "" ? undefined : value,
      };
      setParameters(updated);
      onParametersChange(updated);
    } else {
      const sectionData = parameters[section as keyof AnalysisParameters];
      if (typeof sectionData === "object" && sectionData !== null) {
        const updated = {
          ...parameters,
          [section]: {
            ...sectionData,
            [field]: value,
          },
        };
        setParameters(updated);
        onParametersChange(updated);
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(parameters);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Frequency Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Frequency Settings
          </CardTitle>
          <CardDescription>Configure the frequency band for HFO detection</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="low-cutoff" className={labelClass}>
              Low Cutoff (Hz)
              <span title="Lower frequency bound for bandpass filter">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="low-cutoff"
              type="number"
              value={parameters.frequency.low_cutoff}
              onChange={(e) => updateParameter("frequency", "low_cutoff", Number(e.target.value))}
              min={1}
              max={1000}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="high-cutoff" className={labelClass}>
              High Cutoff (Hz)
              <span title="Upper frequency bound for bandpass filter">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="high-cutoff"
              type="number"
              value={parameters.frequency.high_cutoff}
              onChange={(e) => updateParameter("frequency", "high_cutoff", Number(e.target.value))}
              min={1}
              max={1000}
              className={inputClass}
            />
          </div>
        </CardContent>
      </Card>

      {/* Threshold Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Detection Thresholds</CardTitle>
          <CardDescription>Fine-tune HFO detection sensitivity</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="amplitude-1" className={labelClass}>
              Energy Threshold (SD)
              <span title="Standard deviations above mean energy">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="amplitude-1"
              type="number"
              value={parameters.thresholds.amplitude_1}
              onChange={(e) => updateParameter("thresholds", "amplitude_1", Number(e.target.value))}
              min={1}
              max={10}
              step={0.5}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="amplitude-2" className={labelClass}>
              Baseline Threshold (SD)
              <span title="Standard deviations above baseline EEG">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="amplitude-2"
              type="number"
              value={parameters.thresholds.amplitude_2}
              onChange={(e) => updateParameter("thresholds", "amplitude_2", Number(e.target.value))}
              min={1}
              max={10}
              step={0.5}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="peaks-1" className="text-sm font-medium">
              Min Peaks
            </label>
            <input
              id="peaks-1"
              type="number"
              value={parameters.thresholds.peaks_1}
              onChange={(e) => updateParameter("thresholds", "peaks_1", Number(e.target.value))}
              min={1}
              max={20}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="peaks-2" className="text-sm font-medium">
              Min Peaks Above Baseline
            </label>
            <input
              id="peaks-2"
              type="number"
              value={parameters.thresholds.peaks_2}
              onChange={(e) => updateParameter("thresholds", "peaks_2", Number(e.target.value))}
              min={1}
              max={20}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="duration" className="text-sm font-medium">
              Min Duration (ms)
            </label>
            <input
              id="duration"
              type="number"
              value={parameters.thresholds.duration}
              onChange={(e) => updateParameter("thresholds", "duration", Number(e.target.value))}
              min={1}
              max={100}
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="temporal-sync" className="text-sm font-medium">
              Min Separation (ms)
            </label>
            <input
              id="temporal-sync"
              type="number"
              value={parameters.thresholds.temporal_sync}
              onChange={(e) => updateParameter("thresholds", "temporal_sync", Number(e.target.value))}
              min={1}
              max={100}
              className={inputClass}
            />
          </div>
          <div className="space-y-2 col-span-2">
            <label htmlFor="channel-delay" className={labelClass}>
              Channel Delay (ms)
              <span title="Max delay between channels for connectivity">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="spatial-sync"
              type="number"
              value={parameters.thresholds.spatial_sync}
              onChange={(e) => updateParameter("thresholds", "spatial_sync", Number(e.target.value))}
              min={1}
              max={1000}
              className={inputClass}
            />
          </div>
        </CardContent>
      </Card>

      {/* Montage Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Montage Configuration</CardTitle>
          <CardDescription>Select the montage type for analysis</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="montage-type" className="text-sm font-medium">
              Montage Type
            </label>
            <select
              id="montage-type"
              value={parameters.montage.type}
              onChange={(e) => updateParameter("montage", "type", e.target.value)}
              className={inputClass}
            >
              <option value="bipolar">Bipolar</option>
              <option value="average">Average</option>
              <option value="referential">Referential</option>
            </select>
          </div>
          {parameters.montage.type === "referential" && (
            <div className="space-y-2">
              <label htmlFor="reference-channel" className="text-sm font-medium">
                Reference Channel
              </label>
              <select
                id="reference-channel"
                value={parameters.montage.reference || ""}
                onChange={(e) => updateParameter("montage", "reference", e.target.value)}
                className={inputClass}
              >
                <option value="">Select reference channel</option>
                {channels.map((channel) => (
                  <option key={channel} value={channel}>
                    {channel}
                  </option>
                ))}
              </select>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Time Segment */}
      <Card>
        <CardHeader>
          <CardTitle>Time Segment</CardTitle>
          <CardDescription>Define the analysis time window (in seconds)</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="analysis-start" className="text-sm font-medium">
              Start Time (s)
            </label>
            <input
              id="analysis-start"
              type="number"
              value={parameters.analysis_start || 0}
              onChange={(e) => updateParameter("root", "analysis_start", e.target.value ? Number(e.target.value) : "")}
              min={0}
              step={0.1}
              placeholder="0 (beginning)"
              className={inputClass}
            />
          </div>
          <div className="space-y-2">
            <label htmlFor="analysis-end" className={labelClass}>
              End Time (s)
              <span title="Leave empty for entire file">
                <Info className="h-3 w-3 text-gray-400" />
              </span>
            </label>
            <input
              id="analysis-end"
              type="number"
              value={parameters.analysis_end || ""}
              onChange={(e) => updateParameter("root", "analysis_end", e.target.value ? Number(e.target.value) : "")}
              min={0}
              step={0.1}
              placeholder="Auto (end of file)"
              className={inputClass}
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Processing..." : "Start Analysis"}
        </Button>
      </div>
    </form>
  );
};
