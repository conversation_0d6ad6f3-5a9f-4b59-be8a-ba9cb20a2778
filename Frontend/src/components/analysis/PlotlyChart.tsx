'use client';

import React, { useMemo } from 'react';
import Plot from 'react-plotly.js';
import type { HFOEvent } from '@/types/hfo';

interface PlotlyChartProps {
  channelData: Record<string, number[]>;
  visibleChannels: string[];
  hfoEvents: HFOEvent[];
  timeWindow: [number, number];
  samplingRate: number;
  showHFOMarkers: boolean;
  showThresholds?: boolean;
  gain?: number; // Amplitude scaling factor (μV/div)
}

interface PlotlyTrace {
  x: number[];
  y: number[];
  type: 'scatter';
  mode: 'lines' | 'markers';
  name: string;
  line?: { color: string; width: number; dash?: 'dot' | 'dash' | 'solid' };
  marker?: {
    color: string;
    size: number;
    symbol: string;
  };
  showlegend?: boolean;
  hovertemplate?: string;
}


const PlotlyChart: React.FC<PlotlyChartProps> = ({
  channelData,
  visibleChannels,
  hfoEvents,
  timeWindow,
  samplingRate,
  showHFOMarkers,
  showThresholds = false,
  gain = 20, // Default 20 μV/div
}) => {
  const plotData = useMemo(() => {
    const traces: PlotlyTrace[] = [];
    // Scale channel offset based on gain - larger gain means smaller visual separation
    const channelOffset = 200 / (gain / 20); // Base offset scaled by gain ratio

    visibleChannels.forEach((channel, index) => {
      const data = channelData[channel];
      if (!data || data.length === 0) {
        return;
      }

      // Create time axis
      const timeAxis = data.map((_, i) => i / samplingRate);

      // Filter data within time window
      const startIdx = Math.floor(timeWindow[0] * samplingRate);
      const endIdx = Math.floor(timeWindow[1] * samplingRate);
      const windowedTime = timeAxis.slice(startIdx, endIdx);
      const windowedData = data.slice(startIdx, endIdx);

      // Apply gain scaling and add vertical offset for each channel
      // Higher gain = more amplification (zoom in on amplitude)
      const scaledData = windowedData.map(val => (val * 20 / gain)); // Normalize to 20μV/div baseline
      const offsetData = scaledData.map(val => val + index * channelOffset);

      // Main EEG trace
      traces.push({
        x: windowedTime,
        y: offsetData,
        type: 'scatter',
        mode: 'lines',
        name: channel,
        line: { color: '#000000', width: 1 },
        hovertemplate: `${channel}<br>Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
      });

      // Add HFO markers for this channel (simplified to match reference)
      if (showHFOMarkers) {
        const channelHFOs = hfoEvents.filter(
          hfo => hfo.channel === channel &&
          hfo.start_time >= timeWindow[0] &&
          hfo.start_time <= timeWindow[1]
        );

        if (channelHFOs.length > 0) {
          traces.push({
            x: channelHFOs.map(hfo => hfo.start_time),
            y: channelHFOs.map(() => index * channelOffset),
            type: 'scatter',
            mode: 'markers',
            name: `${channel} HFOs`,
            marker: {
              color: '#ef4444',
              size: 8,
              symbol: 'circle',
            },
            showlegend: false,
            hovertemplate: 'HFO Event<br>Time: %{x:.2f}s<extra></extra>',
          });
        }
      }

      // Add amplitude scale lines for each channel
      const baseY = index * channelOffset;
      const scaleLines = [-gain, 0, gain]; // Show -gain, 0, +gain μV lines

      scaleLines.forEach(scaleValue => {
        traces.push({
          x: windowedTime,
          y: new Array(windowedTime.length).fill(baseY + scaleValue),
          type: 'scatter',
          mode: 'lines',
          name: '',
          line: {
            color: scaleValue === 0 ? '#94a3b8' : '#e2e8f0',
            width: scaleValue === 0 ? 1 : 0.5,
            dash: scaleValue === 0 ? 'solid' : 'dot',
          },
          showlegend: false,
          hovertemplate: '',
        });
      });

      // Add threshold lines if enabled
      if (showThresholds) {
        const threshold = index * channelOffset + 50; // Example threshold
        traces.push({
          x: windowedTime,
          y: new Array(windowedTime.length).fill(threshold),
          type: 'scatter',
          mode: 'lines',
          name: `${channel} threshold`,
          line: {
            color: 'rgba(239, 68, 68, 0.3)',
            width: 1,
            dash: 'dot',
          },
          showlegend: false,
          hovertemplate: '',
        });
      }
    });

    return traces;
  }, [channelData, visibleChannels, hfoEvents, timeWindow, samplingRate, showHFOMarkers, showThresholds, gain]);

  const layout: Partial<Plotly.Layout> = {
    title: {
      text: '',
      font: {
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        size: 18,
        color: '#1e293b'
      }
    },
    xaxis: {
      title: {
        text: 'Time (seconds)',
        font: {
          family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
          size: 12,
          color: '#475569'
        }
      },
      rangeslider: { visible: false },
      range: timeWindow,
      gridcolor: '#e2e8f0',
      gridwidth: 0.5,
      linecolor: '#cbd5e1',
      linewidth: 1,
      tickfont: {
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        size: 11,
        color: '#64748b'
      },
      zeroline: false
    },
    yaxis: {
      title: {
        text: 'Channels',
        font: {
          family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
          size: 12,
          color: '#475569'
        }
      },
      tickmode: 'array' as const,
      tickvals: visibleChannels.map((_, i) => i * (200 / (gain / 20))),
      ticktext: visibleChannels.map((channel) => {
        return `${channel}\n±${gain}μV`;
      }),
      tickangle: 0,
      autorange: 'reversed' as const,
      gridcolor: '#f1f5f9',
      gridwidth: 0.5,
      linecolor: '#cbd5e1',
      linewidth: 1,
      tickfont: {
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        size: 11,
        color: '#64748b'
      },
      zeroline: false
    },
    height: 580,
    margin: { l: 100, r: 30, t: 20, b: 50 },
    hovermode: 'closest' as const,
    hoverlabel: {
      bgcolor: 'white',
      font: {
        family: 'var(--font-sans), system-ui, -apple-system, sans-serif',
        size: 12,
        color: '#1e293b'
      },
      bordercolor: '#e2e8f0'
    },
    showlegend: false,
    plot_bgcolor: '#ffffff',
    paper_bgcolor: '#ffffff',
  };

  const config = {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: ['lasso2d' as const, 'select2d' as const, 'autoScale2d' as const],
    modeBarButtonsToAdd: [],
    toImageButtonOptions: {
      format: 'png' as const,
      filename: 'eeg_analysis',
      height: 1080,
      width: 1920,
      scale: 2,
    },
    responsive: true,
  };

  return (
    <Plot
      data={plotData || []}
      layout={layout}
      config={config}
      useResizeHandler
      style={{ width: '100%', height: '100%' }}
    />
  );
};

export default PlotlyChart;