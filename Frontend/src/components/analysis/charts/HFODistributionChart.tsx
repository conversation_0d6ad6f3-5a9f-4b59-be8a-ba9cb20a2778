import React from "react";
import Plot from "react-plotly.js";

interface HFODistributionChartProps {
  hfoRatePerChannel: Record<string, number>;
}

export const HFODistributionChart: React.FC<HFODistributionChartProps> = ({
  hfoRatePerChannel,
}) => {
  const plotData = [
    {
      type: "bar" as const,
      x: Object.keys(hfoRatePerChannel),
      y: Object.values(hfoRatePerChannel),
      marker: { color: "#3b82f6" }
    }
  ];

  return (
    <div className="border rounded-lg p-4">
      <h3 className="text-lg font-semibold mb-3">HFO Distribution by Channel</h3>
      <Plot
        data={plotData}
        layout={{
          height: 250,
          xaxis: { title: { text: "Channel" } },
          yaxis: { title: { text: "Number of HFOs" } },
          showlegend: false
        }}
        config={{ responsive: true }}
        style={{ width: "100%" }}
      />
    </div>
  );
};