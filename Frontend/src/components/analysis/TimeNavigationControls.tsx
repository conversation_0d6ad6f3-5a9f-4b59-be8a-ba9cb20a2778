import React from 'react';
import { ChevronLeft, ChevronRight, RotateCcw, ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';

interface TimeNavigationControlsProps {
  timeWindow: [number, number];
  timeWindowSize: number;
  totalDuration: number;
  onTimeWindowChange: (window: [number, number]) => void;
  onTimeWindowSizeChange: (size: number) => void;
  onReset: () => void;
  onFullscreen?: () => void;
  currentChunk?: number;
  totalChunks?: number;
}

export const TimeNavigationControls: React.FC<TimeNavigationControlsProps> = ({
  timeWindow,
  timeWindowSize,
  totalDuration,
  onTimeWindowChange,
  onTimeWindowSizeChange,
  onReset,
  onFullscreen,
  currentChunk = 0,
  totalChunks = Math.ceil(totalDuration / 10),
}) => {
  const canNavigatePrev = timeWindow[0] > 0;
  const canNavigateNext = timeWindow[1] < totalDuration;
  const canZoomIn = timeWindowSize > 1;
  const canZoomOut = timeWindowSize < 60;

  const handleNavigate = (direction: 'prev' | 'next') => {
    const step = timeWindowSize / 2;
    if (direction === 'prev') {
      const newStart = Math.max(0, timeWindow[0] - step);
      onTimeWindowChange([newStart, newStart + timeWindowSize]);
    } else {
      const newStart = Math.min(totalDuration - timeWindowSize, timeWindow[0] + step);
      onTimeWindowChange([newStart, newStart + timeWindowSize]);
    }
  };

  const handleZoom = (direction: 'in' | 'out') => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newSize = direction === 'in'
      ? Math.max(1, timeWindowSize / 2)
      : Math.min(60, timeWindowSize * 2);

    const newStart = Math.max(0, Math.min(totalDuration - newSize, center - newSize / 2));
    onTimeWindowSizeChange(newSize);
    onTimeWindowChange([newStart, newStart + newSize]);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3">
      <div className="flex items-center justify-between gap-4">
        {/* Navigation Controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleNavigate('prev')}
            disabled={!canNavigatePrev}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Previous (O)"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>

          <div className="flex flex-col items-center px-3">
            <span className="text-xs text-gray-500">Chunk {currentChunk + 1} of {totalChunks}</span>
            <span className="text-sm font-semibold">
              {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
            </span>
          </div>

          <button
            onClick={() => handleNavigate('next')}
            disabled={!canNavigateNext}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Next (P)"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        {/* Window Size Selector */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">Window Size:</span>
          <select
            value={timeWindowSize}
            onChange={(e) => {
              const newSize = Number(e.target.value);
              const currentCenter = (timeWindow[0] + timeWindow[1]) / 2;
              const newStart = Math.max(0, Math.min(totalDuration - newSize, currentCenter - newSize / 2));
              onTimeWindowSizeChange(newSize);
              onTimeWindowChange([newStart, newStart + newSize]);
            }}
            className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
          >
            <option value={1}>1s</option>
            <option value={5}>5s</option>
            <option value={10}>10s</option>
            <option value={30}>30s</option>
          </select>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => handleZoom('in')}
            disabled={!canZoomIn}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Zoom In (A)"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleZoom('out')}
            disabled={!canZoomOut}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Zoom Out (D)"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
        </div>

        {/* Position Indicator */}
        <div className="flex-1 max-w-xs">
          <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="absolute h-full bg-black rounded-full transition-all duration-300"
              style={{
                left: `${(timeWindow[0] / totalDuration) * 100}%`,
                width: `${((timeWindow[1] - timeWindow[0]) / totalDuration) * 100}%`,
              }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0s</span>
            <span>{totalDuration.toFixed(0)}s</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-1">
          <button
            onClick={onReset}
            className="p-2 rounded hover:bg-gray-100 transition-colors"
            title="Reset View"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          {onFullscreen && (
            <button
              onClick={onFullscreen}
              className="p-2 rounded hover:bg-gray-100 transition-colors"
              title="Fullscreen (F)"
            >
              <Maximize2 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className="mt-2 pt-2 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          Keyboard shortcuts: <span className="font-medium">A</span> Zoom In • <span className="font-medium">D</span> Zoom Out • <span className="font-medium">O</span> Previous • <span className="font-medium">P</span> Next • <span className="font-medium">F</span> Fullscreen
        </div>
      </div>
    </div>
  );
};