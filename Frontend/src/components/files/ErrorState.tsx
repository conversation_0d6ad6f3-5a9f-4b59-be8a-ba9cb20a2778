import React from "react";
import { Button } from "@/components/ui/button";

interface ErrorStateProps {
  error: string;
  onRetry: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({ error, onRetry }) => {
  return (
    <div className="text-center p-8">
      <p className="text-red-600 mb-4">{error}</p>
      <Button onClick={onRetry} variant="default">
        Retry
      </Button>
    </div>
  );
};