import { useAppSelector } from "@/store/hooks";
import { selectFiles } from "@/store/slices/filesSlice";
import { DataTable, type TableColumn } from "./DataTable";

interface AnalyzedFile {
  edf: string;
  size: string;
  patient: string;
  srHz: number;
  recordingDate: string;
  recordingStartTime: string;
  dateAnalyzed: string;
  analyzedLength: string;
  analyzedMontage: string;
  analyzedBand: string;
  parametersUsed: string;
}

const columns: TableColumn<AnalyzedFile>[] = [
  { key: "edf", header: "EDF", accessor: (file) => file.edf },
  { key: "size", header: "Size", accessor: (file) => file.size },
  { key: "patient", header: "Patient", accessor: (file) => file.patient },
  { key: "srHz", header: "SR (Hz)", accessor: (file) => String(file.srHz) },
  { key: "recordingDate", header: "Recording date", accessor: (file) => file.recordingDate },
  { key: "recordingStartTime", header: "Recording start time", accessor: (file) => file.recordingStartTime },
  { key: "dateAnalyzed", header: "Date analyzed", accessor: (file) => file.dateAnalyzed },
  { key: "analyzedLength", header: "Analyzed length", accessor: (file) => file.analyzedLength },
  { key: "analyzedMontage", header: "Analyzed montage", accessor: (file) => file.analyzedMontage },
  { key: "analyzedBand", header: "Analyzed band", accessor: (file) => file.analyzedBand },
  { key: "parametersUsed", header: "Parameters used", accessor: (file) => file.parametersUsed },
];

export function AnalyzedFilesTable() {
  const { analyzedFiles, isLoading } = useAppSelector(selectFiles);
  
  return (
    <DataTable
      title="Files Analyzed"
      columns={columns}
      data={analyzedFiles}
      loading={isLoading}
      emptyMessage="No analyzed files available"
      getRowKey={(file) => file.edf}
    />
  );
}