import React from "react";

interface UploadProgressBarProps {
  progress: number;
}

export const UploadProgressBar: React.FC<UploadProgressBarProps> = ({ progress }) => {
  return (
    <div className="w-full">
      <div className="mb-2">Uploading... {progress}%</div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
          style={{ width: `${progress}%` }} 
        />
      </div>
    </div>
  );
};