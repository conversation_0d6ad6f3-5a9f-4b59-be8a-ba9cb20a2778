import React from "react";
import { Upload } from "lucide-react";
import { FILE_CONFIG } from "@/constants";

interface DropZoneProps {
  inputId: string;
}

export const DropZone: React.FC<DropZoneProps> = ({ inputId }) => {
  return (
    <label htmlFor={inputId} className="cursor-pointer">
      <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
      <p className="text-lg font-medium mb-2">Drag and drop your EDF file here</p>
      <p className="text-sm text-gray-500 mb-4">or click to browse</p>
      <p className="text-xs text-gray-400">
        Only {FILE_CONFIG.ALLOWED_EXTENSIONS.join(", ")} files up to {FILE_CONFIG.MAX_SIZE_MB}MB are supported
      </p>
    </label>
  );
};