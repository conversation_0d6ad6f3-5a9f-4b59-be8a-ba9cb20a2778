import React from "react";
import { FileIcon, CheckCircle } from "lucide-react";
import { formatFileSize } from "@/utils/file";
import { UploadProgressBar } from "./UploadProgressBar";
import { <PERSON><PERSON> } from "@/components/ui/button";

interface FilePreviewProps {
  file: File;
  uploadStatus: "idle" | "uploading" | "success" | "error";
  uploadProgress: number;
  onUpload: () => void;
  onCancel: () => void;
}

export const FilePreview: React.FC<FilePreviewProps> = ({
  file,
  uploadStatus,
  uploadProgress,
  onUpload,
  onCancel,
}) => {
  return (
    <div>
      <FileIcon className="w-12 h-12 mx-auto mb-4 text-blue-500" />
      <p className="text-lg font-medium mb-2">{file.name}</p>
      <p className="text-sm text-gray-500 mb-4">{formatFileSize(file.size)}</p>

      {uploadStatus === "idle" && (
        <div className="flex gap-2 justify-center">
          <Button onClick={onUpload} variant="default">
            Upload
          </Button>
          <Button onClick={onCancel} variant="secondary">
            Cancel
          </Button>
        </div>
      )}

      {uploadStatus === "uploading" && <UploadProgressBar progress={uploadProgress} />}

      {uploadStatus === "success" && (
        <div className="flex items-center justify-center text-green-600">
          <CheckCircle className="w-5 h-5 mr-2" />
          Upload successful!
        </div>
      )}
    </div>
  );
};