import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";

const HEADER_HEIGHT = "h-16";
const HEADER_BG = "bg-white";
const LOGO_COLOR = "text-blue-600";
const BRAND_NAME = "biormika | Oscillyzer";

interface HeaderProps {
  username?: string;
  credits?: number;
}

export function Header({ username = "User", credits = 0 }: HeaderProps) {
  const handleLogout = () => {
    
  };

  return (
    <header className={`${HEADER_HEIGHT} ${HEADER_BG} border-b border-gray-200 flex items-center justify-between px-6`}>
      <div className="flex items-center gap-2">
        <div className="w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center bg-orange-500 text-white text-xs">
          logo
        </div>
        <h1 className={`text-2xl font-semibold ${LOGO_COLOR}`}>
          {BRAND_NAME}
        </h1>
      </div>

      <div className="flex items-center gap-6">
        <span className="text-sm text-gray-600">
          Welcome <strong>{username}</strong>! <strong>{credits}</strong> credits available
        </span>
        <Button 
          onClick={handleLogout}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <LogOut className="w-4 h-4 mr-2" />
          Log Out
        </Button>
      </div>
    </header>
  );
}