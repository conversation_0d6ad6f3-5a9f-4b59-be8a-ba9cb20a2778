import { Home, Activity, BarChart2, Settings, User } from "lucide-react";
import { NavLink } from "react-router-dom";

const SIDEBAR_WIDTH = "w-64";
const SIDEBAR_BG = "bg-gradient-to-b from-purple-50 to-purple-100";

interface SidebarProps {
  className?: string;
}

export function Sidebar({ className = "" }: SidebarProps) {
  const navItems = [
    { to: "/", icon: Home, label: "Home" },
    { to: "/analysis", icon: Activity, label: "Analysis" },
    { to: "/waveforms", icon: BarChart2, label: "Waveforms" },
    { to: "/settings", icon: Settings, label: "Settings" },
    { to: "/account", icon: User, label: "Account" },
  ];

  return (
    <aside className={`${SIDEBAR_WIDTH} ${SIDEBAR_BG} h-full flex flex-col ${className}`}>
      <nav className="flex-1 px-4 py-8">
        <ul className="space-y-2">
          {navItems.map((item) => (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) =>
                  `flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
                    isActive
                      ? "bg-white shadow-sm text-purple-700 font-medium"
                      : "text-gray-700 hover:bg-purple-200/50"
                  }`
                }
              >
                <item.icon className="w-5 h-5" />
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}