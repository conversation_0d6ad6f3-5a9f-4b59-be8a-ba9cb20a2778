export type ToastType = "success" | "error" | "info" | "warning";

export interface ToastState {
  isOpen: boolean;
  message: string;
  type: ToastType;
}

export interface DialogState {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
}

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}