export type HFOType = 'accepted' | 'rejected' | 'rejected_long' | 'lfo_rejected' | 'noise_rejected';

export interface HFOEvent {
  channel: string;
  type: HFOType;
  start_time: number;
  end_time: number;
  peak_frequency: number;
  amplitude: number;
  duration_ms?: number;
  power?: number;
}

export const HFO_TYPE_COLORS: Record<HFOType, string> = {
  accepted: '#ef4444',        // Red for accepted HFOs
  rejected: '#3b82f6',         // Blue for rejected HFOs
  rejected_long: '#3b82f6',    // Blue for long rejected HFOs
  lfo_rejected: '#6b7280',     // Gray for low frequency oscillations
  noise_rejected: '#9ca3af',   // Light gray for noise-related rejections
};

export const HFO_TYPE_LABELS: Record<HFOType, string> = {
  accepted: 'Accepted HFOs',
  rejected: 'Rejected (few peaks)',
  rejected_long: 'Rejected (long duration)',
  lfo_rejected: 'Low Frequency Oscillations',
  noise_rejected: 'Noise-related',
};

export interface HFOStatistics {
  total_hfos: number;
  accepted_count: number;
  rejected_count: number;
  rejected_long_count: number;
  lfo_rejected_count: number;
  noise_rejected_count: number;
}