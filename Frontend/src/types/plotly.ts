// Plotly type definitions for our visualization
export interface PlotlyTrace {
  x: number[];
  y: number[];
  type: 'scatter';
  mode: 'lines' | 'markers' | 'lines+markers';
  name: string;
  line?: {
    color: string;
    width: number;
    dash?: 'solid' | 'dot' | 'dash' | 'longdash' | 'dashdot' | 'longdashdot';
  };
  marker?: {
    color: string;
    size: number;
    symbol?: string;
  };
  showlegend?: boolean;
  hovertemplate?: string;
}