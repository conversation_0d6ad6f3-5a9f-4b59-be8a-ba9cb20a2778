import { useState, useCallback } from "react";
import { useToast } from "@/hooks/useToast";
import { FILE_CONFIG } from "@/constants";
import { validateMultipleFiles } from "@/utils/validation";
import { useUploadFileMutation } from "@/services/apiSlice";

interface UseFileUploadOptions {
  onSuccess?: (files: File[]) => void;
  onError?: (error: Error) => void;
  maxSize?: number;
  allowedExtensions?: string[];
}

export function useFileUpload({
  onSuccess,
  onError,
  maxSize = FILE_CONFIG.MAX_SIZE_BYTES,
  allowedExtensions = FILE_CONFIG.ALLOWED_EXTENSIONS,
}: UseFileUploadOptions = {}) {
  const [uploadProgress, setUploadProgress] = useState(0);
  const { showToast } = useToast();
  const [uploadFileMutation, { isLoading }] = useUploadFileMutation();

  const validateFiles = useCallback((files: File[]) => validateMultipleFiles(files, maxSize, allowedExtensions), [allowedExtensions, maxSize]);

  const uploadFile = useCallback(
    async (files: File[]) => {
      const { valid, errors } = validateFiles(files);

      if (errors.length > 0) {
        errors.forEach((error) => showToast(error, "error"));
        if (onError) {
          onError(new Error(errors.join("\n")));
        }
        return;
      }

      if (valid.length === 0) {
        return;
      }

      setUploadProgress(0);

      try {
        for (const file of valid) {
          await uploadFileMutation({
            file,
            onProgress: (progress: number) => setUploadProgress(progress),
          }).unwrap();
        }

        showToast(`Successfully uploaded ${valid.length} file(s)`, "success");
        if (onSuccess) {
          onSuccess(valid);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Upload failed";
        showToast(errorMessage, "error");
        if (onError) {
          onError(error instanceof Error ? error : new Error(errorMessage));
        }
      } finally {
        setUploadProgress(0);
      }
    },
    [validateFiles, showToast, onSuccess, onError, uploadFileMutation]
  );

  return {
    uploadFile,
    isUploading: isLoading,
    uploadProgress,
    validateFiles,
  };
}
