import { useCallback, useState } from "react";
import { useToast } from "./useToast";
import { useConfirmDialog } from "./useConfirmDialog";
import { useDeleteFileMutation, useLazyGetDownloadUrlQuery } from "@/services/apiSlice";
import { SUCCESS_MESSAGES } from "@/constants";

interface UseFileActionsOptions {
  onDeleteSuccess?: () => void;
  onDownloadSuccess?: () => void;
  showConfirmDialog?: (
    title: string,
    message: string,
    onConfirm: () => void,
    options?: {
      confirmText?: string;
      cancelText?: string;
      isDestructive?: boolean;
    }
  ) => void;
}

export function useFileActions(options: UseFileActionsOptions = {}) {
  const [operationInProgress, setOperationInProgress] = useState<string | null>(null);
  const { showToast } = useToast();
  const { showConfirmDialog: defaultShowConfirmDialog } = useConfirmDialog();
  const [deleteFile] = useDeleteFileMutation();
  const [triggerDownloadUrl] = useLazyGetDownloadUrlQuery();

  const handleDownload = useCallback(
    async (key: string) => {
      setOperationInProgress(`download-${key}`);
      try {
        const res = await triggerDownloadUrl(key).unwrap();
        window.open(res.download_url, "_blank");
        options.onDownloadSuccess?.();
      } finally {
        setOperationInProgress(null);
      }
    },
    [triggerDownloadUrl, options]
  );

  const handleDelete = useCallback(
    (key: string, filename: string) => {
      const confirmDialog = options.showConfirmDialog || defaultShowConfirmDialog;
      confirmDialog(
        "Delete File",
        `Are you sure you want to delete ${filename}?`,
        async () => {
          setOperationInProgress(`delete-${key}`);
          try {
            await deleteFile(key).unwrap();
            showToast(SUCCESS_MESSAGES.FILE_DELETED, "success");
            options.onDeleteSuccess?.();
          } finally {
            setOperationInProgress(null);
          }
        },
        {
          confirmText: "Delete",
          cancelText: "Cancel",
          isDestructive: true,
        }
      );
    },
    [defaultShowConfirmDialog, deleteFile, showToast, options]
  );

  const isOperationInProgress = useCallback(
    (type: "download" | "delete", key: string) => {
      return operationInProgress === `${type}-${key}`;
    },
    [operationInProgress]
  );

  return {
    handleDownload,
    handleDelete,
    isOperationInProgress,
    operationInProgress,
  };
}
