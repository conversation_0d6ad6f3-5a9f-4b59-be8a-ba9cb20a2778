import { useState, type DragEvent, useCallback } from 'react';

interface UseDragAndDropOptions {
  onDrop?: (files: File[]) => void;
  accept?: string[];
  multiple?: boolean;
}

export function useDragAndDrop({
  onDrop,
  accept = [],
  multiple = true,
}: UseDragAndDropOptions = {}) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragging(false);

      const droppedFiles = Array.from(e.dataTransfer.files);
      
      let validFiles = droppedFiles;
      
      if (accept.length > 0) {
        validFiles = droppedFiles.filter(file => {
          const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`;
          return accept.some(ext => ext.toLowerCase() === fileExtension);
        });
      }

      if (!multiple && validFiles.length > 0) {
        validFiles = [validFiles[0]];
      }

      if (validFiles.length > 0 && onDrop) {
        onDrop(validFiles);
      }
    },
    [accept, multiple, onDrop]
  );

  return {
    isDragging,
    dragHandlers: {
      onDragOver: handleDragOver,
      onDragLeave: handleDragLeave,
      onDrop: handleDrop,
    },
  };
}