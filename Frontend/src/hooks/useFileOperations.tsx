import { useCallback, useMemo } from "react";
import { useToast } from "./useToast";
import { SUCCESS_MESSAGES } from "@/constants";
import type { FileInfo } from "@/types/api";
import { useGetFilesQuery, useDeleteFileMutation, useLazyGetDownloadUrlQuery } from "@/services/apiSlice";

export function useFileOperations() {
  const { showToast } = useToast();

  const { data, isLoading, error, refetch } = useGetFilesQuery();
  const [deleteFileMutation] = useDeleteFileMutation();
  const [triggerDownloadUrl] = useLazyGetDownloadUrlQuery();

  const files: FileInfo[] = useMemo(() => {
    if (!data?.files) return [];
    return data.files;
  }, [data]);

  const fetchFiles = useCallback(async () => {
    try {
      await refetch().unwrap();
    } catch {
      showToast("Failed to fetch files", "error");
    }
  }, [refetch, showToast]);

  const deleteFile = useCallback(
    async (key: string) => {
      try {
        await deleteFileMutation(key).unwrap();
        showToast(SUCCESS_MESSAGES.FILE_DELETED, "success");
        return true;
      } catch {
        showToast("Failed to delete file", "error");
        return false;
      }
    },
    [deleteFileMutation, showToast]
  );

  const getDownloadUrl = useCallback(
    async (key: string) => {
      try {
        const res = await triggerDownloadUrl(key).unwrap();
        return res.download_url;
      } catch {
        showToast("Failed to generate download link", "error");
        return null;
      }
    },
    [triggerDownloadUrl, showToast]
  );

  return {
    files,
    loading: isLoading,
    error: error ? "Failed to load files" : null,
    fetchFiles,
    deleteFile,
    getDownloadUrl,
  };
}
