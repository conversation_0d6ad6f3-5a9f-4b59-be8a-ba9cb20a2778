import { configureStore } from "@reduxjs/toolkit";
import userReducer from "./slices/userSlice";
import filesReducer from "./slices/filesSlice";
import analysisReducer from "./slices/analysisSlice";
import { apiSlice } from "@/services/apiSlice";

export const store = configureStore({
  reducer: {
    user: userReducer,
    files: filesReducer,
    analysis: analysisReducer,
    [apiSlice.reducerPath]: apiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          `${apiSlice.reducerPath}/executeMutation/pending`,
          `${apiSlice.reducerPath}/executeMutation/fulfilled`,
          `${apiSlice.reducerPath}/executeMutation/rejected`,
        ],
        ignoredActionPaths: ["meta.arg.originalArgs.file", "meta.arg.originalArgs.onProgress"],
      },
    }).concat(apiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
