import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';
import { UI_CONSTANTS } from '@/constants';

interface UserState {
  username: string;
  credits: number;
  isAuthenticated: boolean;
}

const initialState: UserState = {
  username: UI_CONSTANTS.DEFAULT_USER_DISPLAY,
  credits: UI_CONSTANTS.DEFAULT_USER_CREDITS,
  isAuthenticated: true,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<{ username: string; credits: number }>) => {
      state.username = action.payload.username;
      state.credits = action.payload.credits;
      state.isAuthenticated = true;
    },
    updateCredits: (state, action: PayloadAction<number>) => {
      state.credits = action.payload;
    },
    logout: (state) => {
      state.username = '';
      state.credits = 0;
      state.isAuthenticated = false;
    },
  },
});

export const { setUser, updateCredits, logout } = userSlice.actions;

export const selectUser = (state: RootState) => state.user;

export default userSlice.reducer;