import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../index';

interface AnalysisResult {
  id: string;
  fileName: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  results: Record<string, unknown> | null;
  error?: string;
}

interface AnalysisSettings {
  montage: string;
  frequency: {
    min: number;
    max: number;
  };
  duration: number;
  parameters: Record<string, unknown>;
}

interface AnalysisState {
  currentAnalysis: AnalysisResult | null;
  analysisHistory: AnalysisResult[];
  settings: AnalysisSettings | null;
  isAnalyzing: boolean;
}

const initialState: AnalysisState = {
  currentAnalysis: null,
  analysisHistory: [],
  settings: null,
  isAnalyzing: false,
};

export const analysisSlice = createSlice({
  name: 'analysis',
  initialState,
  reducers: {
    startAnalysis: (state, action: PayloadAction<{ id: string; fileName: string }>) => {
      state.currentAnalysis = {
        id: action.payload.id,
        fileName: action.payload.fileName,
        status: 'pending',
        progress: 0,
        results: null,
      };
      state.isAnalyzing = true;
    },
    updateAnalysisProgress: (state, action: PayloadAction<number>) => {
      if (state.currentAnalysis) {
        state.currentAnalysis.progress = action.payload;
        state.currentAnalysis.status = 'processing';
      }
    },
    completeAnalysis: (state, action: PayloadAction<Record<string, unknown>>) => {
      if (state.currentAnalysis) {
        state.currentAnalysis.status = 'completed';
        state.currentAnalysis.progress = 100;
        state.currentAnalysis.results = action.payload;
        state.analysisHistory.push(state.currentAnalysis);
      }
      state.isAnalyzing = false;
    },
    failAnalysis: (state, action: PayloadAction<string>) => {
      if (state.currentAnalysis) {
        state.currentAnalysis.status = 'failed';
        state.currentAnalysis.error = action.payload;
        state.analysisHistory.push(state.currentAnalysis);
      }
      state.isAnalyzing = false;
    },
    setAnalysisSettings: (state, action: PayloadAction<AnalysisSettings>) => {
      state.settings = action.payload;
    },
    clearCurrentAnalysis: (state) => {
      state.currentAnalysis = null;
      state.isAnalyzing = false;
    },
    clearAnalysisHistory: (state) => {
      state.analysisHistory = [];
    },
  },
});

export const {
  startAnalysis,
  updateAnalysisProgress,
  completeAnalysis,
  failAnalysis,
  setAnalysisSettings,
  clearCurrentAnalysis,
  clearAnalysisHistory,
} = analysisSlice.actions;

export const selectAnalysis = (state: RootState) => state.analysis;

export default analysisSlice.reducer;