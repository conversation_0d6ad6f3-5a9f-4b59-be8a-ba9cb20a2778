import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../index";
import type { AnalyzedFile, UploadedFile, FilesState } from "@/types/files";

const initialState: FilesState = {
  analyzedFiles: [],
  uploadedFiles: [],
  selectedFile: null,
  isLoading: false,
  error: null,
  operations: {
    uploading: false,
    deleting: null,
    fetchingDownload: null,
  },
};

// With RTK Query handling server data, this slice remains for UI-local state
// like analyzed files and current selections.

export const filesSlice = createSlice({
  name: "files",
  initialState,
  reducers: {
    setAnalyzedFiles: (state, action: PayloadAction<AnalyzedFile[]>) => {
      state.analyzedFiles = action.payload;
    },
    setUploadedFiles: (state, action: PayloadAction<UploadedFile[]>) => {
      state.uploadedFiles = action.payload;
    },
    addAnalyzedFile: (state, action: PayloadAction<AnalyzedFile>) => {
      state.analyzedFiles.push(action.payload);
    },
    addUploadedFile: (state, action: PayloadAction<UploadedFile>) => {
      state.uploadedFiles.push(action.payload);
    },
    removeUploadedFile: (state, action: PayloadAction<string>) => {
      state.uploadedFiles = state.uploadedFiles.filter((file) => file.key !== action.payload);
    },
    setSelectedFile: (state, action: PayloadAction<UploadedFile | null>) => {
      state.selectedFile = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearFiles: (state) => {
      state.analyzedFiles = [];
      state.uploadedFiles = [];
      state.selectedFile = null;
      state.error = null;
    },
  },
  extraReducers: () => {},
});

export const { setAnalyzedFiles, setUploadedFiles, addAnalyzedFile, addUploadedFile, removeUploadedFile, setSelectedFile, clearError, clearFiles } =
  filesSlice.actions;

export const selectFiles = (state: RootState) => state.files;

export default filesSlice.reducer;
