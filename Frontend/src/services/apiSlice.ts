import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { API_CONFIG } from "@/constants";
import type { FileListResponse, FileDeleteResponse, MultipartInitiateResponse, MultipartBatchPartUrlsResponse, CompletedPart } from "@/types/api";
import { MultipartUploadService } from "./multipartUploadService";

export const apiSlice = createApi({
  reducerPath: "api",
  baseQuery: fetchBaseQuery({
    baseUrl: API_CONFIG.BASE_URL,
    prepareHeaders: (headers) => {
      headers.set("Content-Type", "application/json");
      return headers;
    },
  }),
  tagTypes: ["Files"],
  endpoints: (builder) => ({
    getFiles: builder.query<FileListResponse, void>({
      query: () => `${API_CONFIG.ENDPOINTS.FILES}/list`,
      providesTags: ["Files"],
    }),

    deleteFile: builder.mutation<FileDeleteResponse, string>({
      query: (key) => ({
        url: `${API_CONFIG.ENDPOINTS.FILES}/delete`,
        method: "DELETE",
        body: { key },
      }),
      invalidatesTags: ["Files"],
    }),

    getDownloadUrl: builder.query<{ download_url: string; filename: string }, string>({
      query: (key) => `${API_CONFIG.ENDPOINTS.FILES}/download/${encodeURIComponent(key)}`,
    }),

    initiateMultipart: builder.mutation<MultipartInitiateResponse, { filename: string; filesize: number }>({
      query: (body) => ({
        url: `${API_CONFIG.ENDPOINTS.FILES}/multipart/initiate`,
        method: "POST",
        body,
      }),
    }),

    getBatchPartUploadUrls: builder.mutation<MultipartBatchPartUrlsResponse, { key: string; upload_id: string; part_numbers: number[] }>({
      query: (body) => ({
        url: `${API_CONFIG.ENDPOINTS.FILES}/multipart/part-urls`,
        method: "POST",
        body,
      }),
    }),

    completeMultipart: builder.mutation<void, { key: string; upload_id: string; parts: CompletedPart[] }>({
      query: (body) => ({
        url: `${API_CONFIG.ENDPOINTS.FILES}/multipart/complete`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["Files"],
    }),

    abortMultipart: builder.mutation<void, { key: string; upload_id: string }>({
      query: (body) => ({
        url: `${API_CONFIG.ENDPOINTS.FILES}/multipart/abort`,
        method: "POST",
        body,
      }),
    }),

    uploadFile: builder.mutation<{ key: string; upload_id: string }, { file: File; onProgress?: (p: number) => void }>({
      async queryFn(arg) {
        try {
          const result = await MultipartUploadService.uploadFile(arg.file, arg.onProgress);
          return { data: result };
        } catch (error) {
          return { error: { status: "CUSTOM_ERROR", data: (error as Error).message } as unknown as never };
        }
      },
      invalidatesTags: ["Files"],
    }),
  }),
});

export const {
  useGetFilesQuery,
  useDeleteFileMutation,
  useGetDownloadUrlQuery,
  useLazyGetDownloadUrlQuery,
  useInitiateMultipartMutation,
  useGetBatchPartUploadUrlsMutation,
  useCompleteMultipartMutation,
  useAbortMultipartMutation,
  useUploadFileMutation,
} = apiSlice;
