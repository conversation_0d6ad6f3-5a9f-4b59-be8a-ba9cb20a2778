/**
 * File operations service for managing file uploads and downloads
 */

import { fileService } from "./api";
import { MultipartUploadService } from "./multipartUploadService";
import { ERROR_MESSAGES } from "@/constants";
import type { FileInfo } from "@/types/api";

export type OperationResult<T = void> = {
  success: boolean;
  data?: T;
  error?: string;
};

export class FileOperationsService {
  /**
   * Upload a file using multipart upload
   * All files now use multipart upload for consistency and reliability
   */
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<OperationResult> {
    try {
      // Always use multipart upload for all files
      // This ensures consistent behavior and better error recovery
      // Even small files benefit from the robust retry and progress tracking
      await MultipartUploadService.uploadFile(file, onProgress);
      return { success: true };
    } catch (error) {
      // Abort is handled within MultipartUploadService
      return this.handleError(error, ERROR_MESSAGES.UPLOAD_FAILED);
    }
  }

  /**
   * Delete a file from storage
   */
  static async deleteFile(key: string): Promise<OperationResult> {
    try {
      await fileService.deleteFile(key);
      return { success: true };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.DELETE_FAILED);
    }
  }

  /**
   * Fetch list of files from storage
   */
  static async fetchFiles(): Promise<OperationResult<FileInfo[]>> {
    try {
      const response = await fileService.listFiles();
      return { success: true, data: response.files };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.FETCH_FAILED);
    }
  }

  /**
   * Get download URL for a file
   */
  static async getDownloadUrl(key: string): Promise<OperationResult<string>> {
    try {
      const response = await fileService.getDownloadUrl(key);
      return { success: true, data: response.download_url };
    } catch (error) {
      return this.handleError(error, ERROR_MESSAGES.DOWNLOAD_FAILED);
    }
  }

  /**
   * Perform generic file action with error handling
   */
  static async performFileAction<T>(
    action: () => Promise<T>,
    errorMessage?: string
  ): Promise<OperationResult<T>> {
    try {
      const result = await action();
      return { success: true, data: result };
    } catch (error) {
      return this.handleError(error, errorMessage || "Operation failed");
    }
  }

  /**
   * Handle errors consistently
   */
  private static handleError<T = void>(error: unknown, defaultMessage: string): OperationResult<T> {
    return {
      success: false,
      error: error instanceof Error ? error.message : defaultMessage,
    };
  }
}

export const fileOperations = FileOperationsService;