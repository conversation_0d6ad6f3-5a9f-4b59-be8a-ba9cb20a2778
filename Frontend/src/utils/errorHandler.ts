/**
 * Unified error handling utility
 */

import { ERROR_MESSAGES } from "../constants";
import axios, { AxiosError } from "axios";

// Error interface
export interface AppError {
  message: string;
  code?: string;
  details?: unknown;
  retryable?: boolean;
}

export class ErrorHandler {
  /**
   * Main error handler
   */
  static handleError(error: unknown): AppError {
    // Axios errors
    if (axios.isAxiosError(error)) {
      return this.handleAxiosError(error);
    }

    // Standard JS errors
    if (error instanceof Error) {
      return this.handleStandardError(error);
    }

    // String errors
    if (typeof error === "string") {
      return { message: error };
    }

    // Object with message
    if (error && typeof error === "object" && "message" in error) {
      return { message: String(error.message), details: error };
    }

    // Unknown errors
    return { message: ERROR_MESSAGES.UNKNOWN_ERROR || "An unknown error occurred", details: error };
  }

  /**
   * Handle Axios-specific errors
   */
  private static handleAxiosError(error: AxiosError): AppError {
    // Response errors (server responded with error status)
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data as Record<string, unknown> | undefined;

      // Extract message from response
      const message = (data?.detail as string) || (data?.message as string) || error.message;

      // Determine if retryable based on status
      const retryable = status >= 500 || status === 429;

      return {
        message,
        code: String(status),
        details: error,
        retryable
      };
    }

    // Request errors (no response received)
    if (error.request) {
      // Timeout errors
      if (error.code === "ECONNABORTED" || error.message.includes("timeout")) {
        return {
          message: "Request timed out. Please try again.",
          code: "TIMEOUT",
          details: error,
          retryable: true
        };
      }

      // Network errors
      return {
        message: ERROR_MESSAGES.NETWORK_ERROR || "Network error occurred",
        code: "NETWORK_ERROR",
        details: error,
        retryable: true
      };
    }

    // Config errors
    return {
      message: error.message,
      code: "CONFIG_ERROR",
      details: error,
      retryable: false
    };
  }

  /**
   * Handle standard JavaScript errors
   */
  private static handleStandardError(error: Error): AppError {
    // Network-related errors
    if (error.message.toLowerCase().includes("network")) {
      return {
        message: ERROR_MESSAGES.NETWORK_ERROR || "Network error occurred",
        code: "NETWORK_ERROR",
        details: error,
        retryable: true
      };
    }

    // Validation errors
    if (error.message.toLowerCase().includes("validation")) {
      return {
        message: error.message,
        code: "VALIDATION_ERROR",
        details: error,
        retryable: false
      };
    }

    // Default error
    return {
      message: error.message,
      details: error,
      retryable: false
    };
  }

  /**
   * Get simple error message
   */
  static getErrorMessage(error: unknown): string {
    return this.handleError(error).message;
  }

  /**
   * Handle API errors with fallback message
   */
  static handleApiError(error: unknown, defaultMessage: string): string {
    const message = this.getErrorMessage(error);
    return message || defaultMessage;
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(error: unknown): boolean {
    const appError = this.handleError(error);
    return appError.retryable || false;
  }

  /**
   * Format error for user display
   */
  static formatForDisplay(error: unknown): string {
    const appError = this.handleError(error);

    // Add retry hint if retryable
    if (appError.retryable) {
      return `${appError.message} Please try again.`;
    }

    return appError.message;
  }
}

// Export convenience functions for backward compatibility
export const handleError = (error: unknown): AppError => ErrorHandler.handleError(error);
export const getErrorMessage = (error: unknown): string => ErrorHandler.getErrorMessage(error);
export const handleApiError = (error: unknown, defaultMessage: string): string => ErrorHandler.handleApiError(error, defaultMessage);