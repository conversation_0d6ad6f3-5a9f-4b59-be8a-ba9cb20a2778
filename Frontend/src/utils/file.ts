const KILOBYTE = 1024;
const FILE_SIZE_UNITS = ["Bytes", "KB", "MB", "GB"];

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const i = Math.floor(Math.log(bytes) / Math.log(KILOBYTE));
  return parseFloat((bytes / Math.pow(KILOBYTE, i)).toFixed(2)) + " " + FILE_SIZE_UNITS[i];
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
