/**
 * File validation utilities
 */

import { FILE_CONFIG, ERROR_MESSAGES } from "@/constants";
import type { ValidationResult, FileValidationResult } from "@/types";

export class FileValidator {
  /**
   * Get file extension
   */
  static getFileExtension(filename: string): string {
    return `.${filename.split(".").pop()?.toLowerCase()}`;
  }

  /**
   * Validate file type
   */
  static validateFileType(file: File, allowedExtensions: string[]): ValidationResult {
    const extension = this.getFileExtension(file.name);

    if (!allowedExtensions.includes(extension)) {
      return {
        valid: false,
        error: ERROR_MESSAGES.INVALID_FILE_TYPE || `Invalid file type: ${extension}`,
      };
    }

    return { valid: true };
  }

  /**
   * Validate file size
   */
  static validateFileSize(file: File, maxSize: number): ValidationResult {
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return {
        valid: false,
        error: ERROR_MESSAGES.FILE_TOO_LARGE || `File size exceeds ${maxSizeMB}MB limit`,
      };
    }

    if (file.size === 0) {
      return {
        valid: false,
        error: "File is empty",
      };
    }

    return { valid: true };
  }

  /**
   * Validate file name
   */
  static validateFileName(filename: string): ValidationResult {
    // Check for invalid characters
    const invalidChars = /[<>:"|?*]/g;
    if (invalidChars.test(filename)) {
      return {
        valid: false,
        error: "File name contains invalid characters",
      };
    }

    // Check for excessive length
    if (filename.length > 255) {
      return {
        valid: false,
        error: "File name is too long",
      };
    }

    return { valid: true };
  }

  /**
   * Comprehensive file validation
   */
  static validateFile(
    file: File,
    options: {
      maxSize?: number;
      allowedExtensions?: string[];
      validateName?: boolean;
    } = {}
  ): ValidationResult {
    const maxSize = options.maxSize ?? FILE_CONFIG.MAX_SIZE_BYTES;
    const allowedExtensions = options.allowedExtensions ?? FILE_CONFIG.ALLOWED_EXTENSIONS;
    const validateName = options.validateName ?? true;

    // Validate file type
    const typeResult = this.validateFileType(file, allowedExtensions);
    if (!typeResult.valid) {
      return typeResult;
    }

    // Validate file size
    const sizeResult = this.validateFileSize(file, maxSize);
    if (!sizeResult.valid) {
      return sizeResult;
    }

    // Validate file name if requested
    if (validateName) {
      const nameResult = this.validateFileName(file.name);
      if (!nameResult.valid) {
        return nameResult;
      }
    }

    return { valid: true };
  }

  /**
   * Validate multiple files
   */
  static validateMultipleFiles(
    files: File[],
    options: {
      maxSize?: number;
      allowedExtensions?: string[];
      validateName?: boolean;
      maxFiles?: number;
    } = {}
  ): FileValidationResult {
    const valid: File[] = [];
    const errors: string[] = [];
    const maxFiles = options.maxFiles;

    // Check max files limit
    if (maxFiles && files.length > maxFiles) {
      errors.push(`Maximum ${maxFiles} files allowed`);
      return { valid: [], errors };
    }

    // Validate each file
    files.forEach((file) => {
      const result = this.validateFile(file, options);
      if (result.valid) {
        valid.push(file);
      } else {
        errors.push(`${file.name}: ${result.error}`);
      }
    });

    return { valid, errors };
  }

  /**
   * Check if file is duplicate
   */
  static isDuplicateFile(file: File, existingFiles: File[]): boolean {
    return existingFiles.some(
      existing =>
        existing.name === file.name &&
        existing.size === file.size &&
        existing.lastModified === file.lastModified
    );
  }

  /**
   * Filter duplicate files
   */
  static filterDuplicates(newFiles: File[], existingFiles: File[]): File[] {
    return newFiles.filter(file => !this.isDuplicateFile(file, existingFiles));
  }
}

// Export convenience functions for backward compatibility
export const validateEDFFile = (file: File): ValidationResult => {
  return FileValidator.validateFile(file, {
    maxSize: FILE_CONFIG.MAX_SIZE_BYTES,
    allowedExtensions: FILE_CONFIG.ALLOWED_EXTENSIONS,
  });
};

export const validateMultipleFiles = (
  files: File[],
  maxSize: number = FILE_CONFIG.MAX_SIZE_BYTES,
  allowedExtensions: string[] = FILE_CONFIG.ALLOWED_EXTENSIONS
): FileValidationResult => {
  return FileValidator.validateMultipleFiles(files, {
    maxSize,
    allowedExtensions,
  });
};