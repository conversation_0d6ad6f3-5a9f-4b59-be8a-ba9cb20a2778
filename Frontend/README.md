# Frontend - User Interface for EEG Analysis

## What This Does

The Frontend is a React-based web application that provides neurologists and researchers with an intuitive interface to upload EEG files, monitor analysis progress, and visualize HFO detection results.

### User Journey

1. **Upload**: Drag-and-drop EDF files (up to 1GB)
2. **Monitor**: Track processing status in real-time
3. **Visualize**: Interactive charts showing detected HFOs
4. **Export**: Download results as CSV/JSON

### Why It Matters

Manual HFO detection requires specialized software and hours of visual inspection. This interface makes the process accessible to clinicians without technical expertise, providing results in minutes instead of hours.

## Key Features

### File Upload Experience
- **Drag-and-drop** zone with visual feedback
- **Progress tracking** for large files (chunked upload)
- **Multi-file support** for batch processing
- **Validation** ensures only EDF files are accepted

### Results Visualization
- **Interactive plots** using Plotly.js for zooming/panning
- **Channel grid** showing HFO counts per brain region
- **Event table** listing all detected HFOs with timestamps
- **Waveform display** with HFO markers overlaid

### User Settings
- **Detection parameters** customization
- **Email notifications** for completed analyses
- **Preference persistence** across sessions

## Quick Start

```bash
# Install and run
npm install
npm run dev  # http://localhost:5173

# Essential commands
npm run build         # Production build
npm run lint          # Check code quality
npm run deploy        # Deploy to S3 + CloudFront
npx tsc --noEmit     # Type checking
```

## Project Structure

```
Frontend/
├── src/
│   ├── components/      # Reusable UI components
│   │   ├── analysis/   # ResultsViewer, HfoEventsTable, EegSignalPlot
│   │   ├── files/      # FileTable, EmptyState, LoadingState
│   │   └── upload/     # DropZone, FilePreview, UploadProgressBar
│   ├── pages/          # Route components
│   │   ├── HomePage.tsx      # File management
│   │   ├── AnalysisPage.tsx  # Job monitoring
│   │   └── WaveformsPage.tsx # Result visualization
│   ├── services/       # API integration
│   │   ├── api.ts             # Axios client
│   │   └── multipartUploadService.ts
│   ├── store/          # Redux state management
│   │   ├── filesSlice.ts     # File state
│   │   ├── analysisSlice.ts  # Job state
│   │   └── userSlice.ts      # Preferences
│   └── types/          # TypeScript definitions
├── public/            # Static assets
└── vite.config.ts    # Build configuration
```

## Understanding the Components

### Upload Flow
```typescript
// 1. User drops file
<DropZone onFilesSelected={handleUpload} />

// 2. Multipart upload for large files
if (file.size > 100_000_000) {
  await multipartUploadService.uploadLargeFile(file);
} else {
  await normalUpload(file);
}

// 3. Progress feedback
<UploadProgressBar progress={65} fileName="patient.edf" />
```

### Results Display
```typescript
// Channel overview
<ChannelGrid
  channels={['Fp1', 'Fp2', 'F3', 'F4']}
  hfoCounts={{Fp1: 23, Fp2: 15, F3: 8, F4: 12}}
/>

// Interactive plot
<EegSignalPlot
  data={signalData}      // Raw EEG
  hfoEvents={events}     // Detected HFOs
  channelName="Fp1"
  samplingRate={256}
/>

// Event details
<HfoEventsTable
  events={[
    {start: 1.234, end: 1.240, amplitude: 125.3},
    {start: 2.567, end: 2.575, amplitude: 98.7}
  ]}
/>
```

## State Management

### Redux Store Structure
```typescript
{
  files: {
    items: FileInfo[],       // Uploaded files
    uploadProgress: number,   // Current upload %
    isLoading: boolean
  },
  analysis: {
    jobs: Job[],             // All analysis jobs
    activeJob: Job | null,   // Currently selected
    results: Results | null  // HFO detection data
  },
  user: {
    email: string,
    preferences: {
      frequency_band: [80, 500],
      threshold: 3.0,
      emailNotifications: true
    }
  }
}
```

## API Integration

### Configuration (vite.config.ts)
```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8000',  // Local backend
      changeOrigin: true
    }
  }
}
```

### Key API Calls
```typescript
// Upload file
await api.post('/files/multipart/initiate', {
  file_name: 'recording.edf',
  file_size: 524288000
});

// Submit analysis
await api.post('/analysis/submit', {
  file_key: 'uploads/recording.edf',
  parameters: userPreferences
});

// Get results
const results = await api.get(`/analysis/jobs/${jobId}/results`);
```

## Deployment

```bash
# Build and deploy
npm run build              # Creates dist/
npm run deploy            # Upload to S3
npm run deploy:invalidate # Clear CloudFront cache

# Manual deployment
aws s3 sync dist/ s3://frontend-bucket --delete
aws cloudfront create-invalidation \
  --distribution-id EXXXXX --paths "/*"
```

## Environment Configuration

### Development (.env.local)
```env
VITE_API_URL=http://localhost:8000/api/v1
VITE_ENABLE_MOCK=false
```

### Production
API URL configured via proxy in vite.config.ts

## UI/UX Guidelines

### Design Principles
- **Medical context**: Clean, professional interface
- **Accessibility**: ARIA labels, keyboard navigation
- **Responsive**: Works on tablets for bedside use
- **Performance**: Lazy loading for large datasets

### Component Library
- **Tailwind CSS**: Utility-first styling
- **Radix UI**: Accessible primitives
- **Lucide Icons**: Medical/scientific icons
- **Plotly.js**: Scientific visualization

## Performance Optimization

- **Code splitting**: Lazy load Plotly.js (2MB)
- **Virtual scrolling**: For large event tables
- **Memoization**: Prevent unnecessary re-renders
- **Debouncing**: Search and filter inputs
- **Compression**: Gzip enabled in production

## Testing

```bash
# Type checking
npx tsc --noEmit

# Linting
npm run lint

# Manual testing
npm run dev
# Test file upload with sample EDF
# Verify charts render correctly
# Check responsive design
```

## Common Issues

| Issue | Solution |
|-------|----------|
| CORS errors | Check proxy config, backend CORS |
| Upload fails | Verify file <1GB, .edf extension |
| Charts not loading | Clear cache, check Plotly import |
| State not updating | Check Redux DevTools |

## Key Dependencies

- **React 18**: UI framework
- **TypeScript**: Type safety
- **Vite**: Build tool (fast HMR)
- **Redux Toolkit**: State management
- **Plotly.js**: Scientific charts
- **Tailwind CSS**: Styling
- **Axios**: HTTP client

## Browser Support

- Chrome 90+ (recommended)
- Firefox 88+
- Safari 14+
- Edge 90+

## License

Copyright 2024 Biormika. All rights reserved.