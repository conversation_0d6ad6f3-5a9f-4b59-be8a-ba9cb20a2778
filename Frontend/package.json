{"name": "biormika-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:prod": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build:prod && aws s3 sync dist/ s3://biormikastack-staticsitestaticsitebucket442ce34f-deeo2wv5vjxn --delete --profile biormika", "deploy:invalidate": "aws cloudfront create-invalidation --distribution-id E24F2M8RSJYOI7 --paths '/*' --profile biormika", "deploy:full": "npm run deploy && npm run deploy:invalidate"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@reduxjs/toolkit": "^2.9.0", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/vite": "^4.1.13", "axios": "^1.12.1", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.544.0", "plotly.js": "^2.27.1", "react": "^19.1.1", "react-dom": "^19.1.1", "react-plotly.js": "^2.6.0", "react-redux": "^9.2.0", "react-router-dom": "^7.9.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/node": "^24.3.2", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/react-plotly.js": "^2.6.3", "@vitejs/plugin-react": "^5.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vite-plugin-node-polyfills": "^0.24.0"}}