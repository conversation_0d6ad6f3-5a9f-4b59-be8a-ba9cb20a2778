#!/bin/bash

# Deploy Frontend Script
# Builds and deploys React app to S3/CloudFront

set -e

echo "🚀 Starting Frontend Deployment..."
echo "=================================="

# Check if running from root directory
if [ ! -d "Frontend" ]; then
    echo "❌ Error: Must run from project root directory"
    exit 1
fi

# Check if CDK outputs exist
if [ ! -f "Infra/cdk-outputs.json" ]; then
    echo "⚠️  Warning: CDK outputs not found. Running infrastructure check..."
    cd Infra
    source .venv/bin/activate 2>/dev/null || (python3 -m venv .venv && source .venv/bin/activate)
    pip install -q -r requirements.txt
    cdk diff --profile biormika > /dev/null 2>&1 || echo "Infrastructure may need deployment"
    cd ..
fi

# Navigate to Frontend directory
cd Frontend

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Get API URL from CDK outputs
if [ -f "../Infra/cdk-outputs.json" ]; then
    API_URL=$(grep "ApiGatewayUrl" ../Infra/cdk-outputs.json | cut -d'"' -f4)
    if [ ! -z "$API_URL" ]; then
        # Create production env file
        echo "🔧 Configuring production environment..."
        echo "VITE_API_BASE_URL=${API_URL}api/v1" > .env.production
        echo "   API URL: ${API_URL}api/v1"
    else
        echo "⚠️  Warning: API URL not found in CDK outputs"
        echo "   Using existing .env.production if available"
    fi
fi

# Run TypeScript check
echo ""
echo "📝 Checking TypeScript types..."
npx tsc --noEmit || {
    echo "⚠️  TypeScript errors detected. Continue anyway? (y/n)"
    read -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
}

# Run linting
echo "🔍 Running ESLint..."
npm run lint || true

# Build for production
echo ""
echo "🏗️  Building production bundle..."
npm run build:prod

# Check if build was successful
if [ ! -d "dist" ]; then
    echo "❌ Error: Build failed - dist directory not created"
    exit 1
fi

# Get bucket name and CloudFront ID from CDK outputs or package.json
if [ -f "../Infra/cdk-outputs.json" ]; then
    BUCKET_NAME=$(grep "SiteBucketName" ../Infra/cdk-outputs.json | cut -d'"' -f4)
    DISTRIBUTION_ID=$(grep "DistributionId" ../Infra/cdk-outputs.json | cut -d'"' -f4)
fi

# Fallback to hardcoded values if not found
if [ -z "$BUCKET_NAME" ]; then
    BUCKET_NAME="biormikastack-staticsitestaticsitebucket442ce34f-deeo2wv5vjxn"
    echo "⚠️  Using fallback bucket name: $BUCKET_NAME"
fi

if [ -z "$DISTRIBUTION_ID" ]; then
    DISTRIBUTION_ID="E24F2M8RSJYOI7"
    echo "⚠️  Using fallback distribution ID: $DISTRIBUTION_ID"
fi

# Deploy to S3
echo ""
echo "📤 Deploying to S3..."
echo "   Bucket: $BUCKET_NAME"

aws s3 sync dist/ s3://$BUCKET_NAME \
    --delete \
    --profile biormika \
    --cache-control "public, max-age=31536000" \
    --exclude "index.html" \
    --exclude "*.json"

# Upload index.html and JSON files with no-cache
aws s3 cp dist/index.html s3://$BUCKET_NAME/index.html \
    --profile biormika \
    --cache-control "no-cache, no-store, must-revalidate" \
    --content-type "text/html"

aws s3 sync dist/ s3://$BUCKET_NAME \
    --profile biormika \
    --cache-control "no-cache, no-store, must-revalidate" \
    --exclude "*" \
    --include "*.json"

echo "✅ S3 deployment complete!"

# Invalidate CloudFront cache
echo ""
echo "🔄 Invalidating CloudFront cache..."
echo "   Distribution: $DISTRIBUTION_ID"

INVALIDATION_ID=$(aws cloudfront create-invalidation \
    --distribution-id $DISTRIBUTION_ID \
    --paths "/*" \
    --profile biormika \
    --query 'Invalidation.Id' \
    --output text)

echo "✅ Cache invalidation started: $INVALIDATION_ID"

# Get CloudFront URL
if [ -f "../Infra/cdk-outputs.json" ]; then
    CF_URL=$(grep "CloudFrontURL" ../Infra/cdk-outputs.json | cut -d'"' -f4)
    if [ ! -z "$CF_URL" ]; then
        echo ""
        echo "🌐 Frontend deployed to: $CF_URL"
        echo ""
        echo "📋 Deployment Summary:"
        echo "===================="
        echo "  Site URL: $CF_URL"
        echo "  S3 Bucket: $BUCKET_NAME"
        echo "  CloudFront ID: $DISTRIBUTION_ID"
        echo "  Invalidation: $INVALIDATION_ID"
        echo ""
        echo "⏳ Note: CloudFront invalidation may take 5-10 minutes to complete"
    fi
fi

# Return to root
cd ..