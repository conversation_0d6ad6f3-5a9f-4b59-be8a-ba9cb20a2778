"""
Job Manager Service
Handles HFO analysis job lifecycle management
"""

import os
import json
import logging
import traceback
import numpy as np
from typing import Any, Dict, Optional, Tuple
from .aws_service import AWSService
from .notification_service import NotificationService

logger = logging.getLogger(__name__)


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles numpy arrays and other numpy types"""

    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(
            obj,
            (
                np.int_,
                np.intc,
                np.intp,
                np.int8,
                np.int16,
                np.int32,
                np.int64,
                np.uint8,
                np.uint16,
                np.uint32,
                np.uint64,
            ),
        ):
            return int(obj)
        if isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            return float(obj)
        if isinstance(obj, (np.complex_, np.complex64, np.complex128)):
            return {"real": obj.real, "imag": obj.imag}
        if isinstance(obj, np.bool_):
            return bool(obj)
        if isinstance(obj, np.void):
            return None
        return super().default(obj)


class JobManager:
    """Manages HFO analysis job lifecycle"""

    def __init__(
        self,
        aws_service: Optional[AWSService] = None,
        notification_service: Optional[NotificationService] = None,
    ):
        """
        Initialize job manager

        Args:
            aws_service: AWS service instance
            notification_service: Notification service instance
        """
        self.aws_service = aws_service or AWSService()
        self.notification_service = notification_service or NotificationService(
            self.aws_service
        )
        self.temp_dir = "/tmp/edf_processing"

    def process_job_message(self, message: Dict) -> bool:
        """
        Process a job message from SQS

        Args:
            message: SQS message dictionary

        Returns:
            True if job processed successfully
        """
        receipt_handle = message.get("ReceiptHandle")
        job_id = None
        user_email = None
        sender_email = None
        file_key = None

        try:
            # Parse message body
            body = json.loads(message["Body"])
            job_id = body.get("job_id")
            file_key = body.get("file_key")
            user_email = body.get("receiver_email") or body.get("user_email")
            sender_email = body.get("sender_email")
            parameters = body.get("parameters", {})

            logger.info(f"Processing job {job_id} for file {file_key}")

            # Update job status to processing
            self.update_job_status(job_id, "processing")

            # Process the job
            success = self.process_job(
                job_id, file_key, parameters, user_email, sender_email)

            if success:
                # Delete message from queue
                self.aws_service.delete_sqs_message(receipt_handle)
                logger.info(f"Successfully processed job {job_id}")
                return True
            else:
                logger.error(f"Failed to process job {job_id}")
                return False

        except Exception as e:
            error_msg = (
                f"Error processing job message: {str(e)}\n{traceback.format_exc()}"
            )
            logger.error(error_msg)

            # Update job as failed if we have job_id
            if job_id:
                self.update_job_status(
                    job_id, "failed", {"error_message": str(e)})

                # Send error notification if we have user email
                if user_email and file_key:
                    self.notification_service.send_error_email(
                        job_id,
                        user_email,
                        file_key,
                        str(e),
                        sender_email=sender_email,
                    )

            return False

    def process_job(
        self,
        job_id: str,
        file_key: str,
        parameters: Dict,
        user_email: Optional[str] = None,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Process a single HFO analysis job

        Args:
            job_id: Job identifier
            file_key: S3 key of the EDF file
            parameters: Analysis parameters
            user_email: User email for notifications

        Returns:
            True if job processed successfully
        """
        parameter_snapshot: Dict[str, Any] = {}
        try:
            # Download file from S3
            local_path = self.download_file(file_key, job_id)

            # Run HFO analysis
            from core.hfo_engine.pipeline import HFODetectionPipeline
            import pyedflib
            from core.hfo_engine.result_formatter import format_results_for_api
            from datetime import datetime

            # Load EDF file with robust fallback for EDF+D (discontinuous) files
            logger.info(f"Loading EDF file: {local_path}")

            data = None
            signal_labels = None
            srate = 256
            header_info = {}

            try:
                edf = pyedflib.EdfReader(local_path)

                n_signals = edf.signals_in_file
                signal_labels = edf.getSignalLabels()
                # Normalize labels to strings
                signal_labels = [
                    lbl.decode("utf-8") if isinstance(lbl,
                                                      (bytes, bytearray)) else lbl
                    for lbl in signal_labels
                ]

                sampling_frequencies = [
                    edf.getSampleFrequency(i) for i in range(n_signals)
                ]
                srate = int(
                    sampling_frequencies[0]) if sampling_frequencies else 256

                # Stack all channels into a 2D numpy array (channels x samples)
                data = np.vstack([edf.readSignal(i) for i in range(n_signals)])

                # Get header info
                header = edf.getHeader()
                start_dt = edf.getStartdatetime()
                header_info = {
                    "patientID": header.get("patientname", "Anonymous"),
                    "startdate": start_dt.strftime("%d.%m.%y") if start_dt else "",
                    "starttime": start_dt.strftime("%H.%M.%S") if start_dt else "",
                    "srate": srate,
                    "label": signal_labels,
                }
                edf.close()

            except Exception as e:
                # pyEDFlib may fail on EDF+D (discontinuous) files; fall back to binary reader
                logger.warning(
                    f"pyEDFlib failed to read EDF, falling back to binary reader: {e}"
                )
                from core.hfo_engine.pyedfreader import edfread as binary_edfread

                hdr, record = binary_edfread(local_path)
                # Ensure float64 dtype and proper shape (channels x samples)
                data = record.astype(np.float64, copy=False)

                n_signals = int(data.shape[0])
                # Frequency may be per-channel; pick first as representative sample rate
                freq = hdr.get("frequency")
                if isinstance(freq, (list, np.ndarray)) and len(freq) > 0:
                    srate = int(np.round(float(freq[0])))
                elif isinstance(freq, (int, float)):
                    srate = int(np.round(float(freq)))
                else:
                    srate = 256

                signal_labels = hdr.get(
                    "label", [f"CH{i+1}" for i in range(n_signals)])
                header_info = {
                    "patientID": hdr.get("patientID", "Anonymous"),
                    "startdate": hdr.get("startdate", ""),
                    "starttime": hdr.get("starttime", ""),
                    "srate": srate,
                    "label": signal_labels,
                }

            # Prepare EEG data (expected by the original algorithm)
            eeg_data = {
                "data": data,
                "nbchan": int(data.shape[0]),
                "srate": srate,
                "chanlocs": signal_labels,
                "times": None,
            }

            # Prepare analysis parameters
            analysis_params = self.prepare_analysis_parameters(parameters)
            parameter_snapshot = self._build_parameter_snapshot(
                analysis_params, parameters
            )
            logger.info(
                f"Resolved parameters for job {job_id}: {parameter_snapshot}"
            )

            # Run HFO detection
            logger.info(f"Running HFO detection for job {job_id}")
            pipeline = HFODetectionPipeline()
            hfo_results = pipeline.run(
                eeg_data=eeg_data, file_path=local_path, parameters=analysis_params
            )

            if not hfo_results.get("success"):
                raise ValueError(
                    f"HFO analysis failed: {hfo_results.get('error', 'Unknown error')}"
                )

            # Format results
            results = format_results_for_api(hfo_results, eeg_data)
            results["edf_header"] = header_info
            metadata = results.get("metadata", {})
            metadata["processing_time"] = datetime.utcnow().timestamp()
            metadata["parameters_used"] = parameter_snapshot
            if parameters:
                metadata["submitted_parameters"] = parameters
            results["metadata"] = metadata

            # Save results
            results_url, result_key = self.save_results(
                job_id, results, file_key, parameters
            )

            # Update job as completed
            self.update_job_status(
                job_id,
                "completed",
                {
                    "results_url": results_url,
                    "result_key": result_key,
                    "hfo_count": results.get("statistics", {}).get("total_hfos", 0),
                    "completed_at": datetime.utcnow().isoformat(),
                    "parameters_used": parameter_snapshot,
                },
            )

            # Send completion email
            if user_email:
                self.notification_service.send_completion_email(
                    job_id,
                    user_email,
                    file_key,
                    results,
                    sender_email=sender_email,
                )

            # Clean up local files
            self.cleanup_local_files(job_id)

            return True

        except Exception as e:
            logger.error(f"Failed to process job {job_id}: {e}")
            failure_attributes = {"error_message": str(e)}
            if parameter_snapshot:
                failure_attributes["parameters_used"] = parameter_snapshot
            self.update_job_status(job_id, "failed", failure_attributes)

            if user_email:
                self.notification_service.send_error_email(
                    job_id,
                    user_email,
                    file_key,
                    str(e),
                    sender_email=sender_email,
                )

            # Clean up on failure
            self.cleanup_local_files(job_id)
            return False

    def download_file(self, file_key: str, job_id: str) -> str:
        """
        Download EDF file from S3

        Args:
            file_key: S3 key of the file
            job_id: Job identifier

        Returns:
            Local path to downloaded file
        """
        local_path = os.path.join(self.temp_dir, f"{job_id}.edf")
        os.makedirs(self.temp_dir, exist_ok=True)
        self.aws_service.download_file_from_s3(file_key, local_path)
        return local_path

    def save_results(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ) -> Tuple[str, str]:
        """
        Save analysis results to S3

        Args:
            job_id: Job identifier
            results: Analysis results
            file_key: Original file S3 key
            parameters: Analysis parameters

        Returns:
            Tuple of (presigned_url, result_key)
        """
        # Save main results JSON
        results_key = f"results/{job_id}/analysis_results.json"
        logger.info(
            f"Saving results to s3://{self.aws_service.s3_bucket_name}/{results_key}"
        )

        self.aws_service.put_object_to_s3(
            json.dumps(
                results, cls=NumpyEncoder), results_key, "application/json"
        )

        # Generate CSV reports if available
        try:
            from core.hfo_engine.report_generator import REPORT_GENERATOR_AVAILABLE

            if REPORT_GENERATOR_AVAILABLE and results.get("edf_header"):
                self.save_comprehensive_report(
                    job_id, results, file_key, parameters)

            # Save simple HFO events CSV
            if results.get("hfo_events"):
                self.save_hfo_events_csv(job_id, results["hfo_events"])

        except ImportError:
            logger.warning("Report generator not available")

        # Generate presigned URL
        presigned_url = self.aws_service.generate_presigned_url(results_key)

        return presigned_url, results_key

    def save_comprehensive_report(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ):
        """Save comprehensive CSV report"""
        try:
            from core.hfo_engine.report_generator import generate_csv_report

            file_name = os.path.basename(file_key).replace(".edf", "")
            frequency_params = parameters.get("frequency", {})

            csv_report = generate_csv_report(
                parameters=results,
                header=results["edf_header"],
                file_name=file_name,
                locutoff=frequency_params.get("low_cutoff", 80),
                hicutoff=frequency_params.get("high_cutoff", 500),
            )

            report_key = f"results/{job_id}/analysis_report.csv"
            self.aws_service.put_object_to_s3(
                csv_report, report_key, "text/csv")
            logger.info(f"Saved comprehensive report to {report_key}")

        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")

    def save_hfo_events_csv(self, job_id: str, hfo_events: list):
        """Save simple CSV of HFO events"""
        import csv
        import io

        output = io.StringIO()
        writer = csv.DictWriter(
            output,
            fieldnames=[
                "channel",
                "start_time",
                "end_time",
                "peak_frequency",
                "amplitude",
            ],
        )
        writer.writeheader()
        writer.writerows(hfo_events)

        csv_key = f"results/{job_id}/hfo_events.csv"
        self.aws_service.put_object_to_s3(
            output.getvalue(), csv_key, "text/csv")
        logger.info(f"Saved HFO events CSV to {csv_key}")

    def prepare_analysis_parameters(self, parameters: Optional[Dict]) -> Dict:
        """
        Prepare analysis parameters with defaults

        Args:
            parameters: User-provided parameters

        Returns:
            Complete parameters dictionary
        """
        parameters = parameters or {}
        frequency_params = parameters.get("frequency") or {}
        thresholds_params = parameters.get("thresholds") or {}
        montage_params = parameters.get("montage") or {}

        def _coalesce(keys, *sources, default=None):
            for key in keys:
                for source in sources:
                    if isinstance(source, dict) and key in source:
                        value = source.get(key)
                        if value is not None and value != "":
                            return value
            return default

        def _to_number(value: Any, default: float, cast_type):
            if value is None:
                return default
            if isinstance(value, (int, float)):
                return cast_type(value)
            try:
                return cast_type(float(value))
            except (TypeError, ValueError):
                return default

        # Resolve analysis window, falling back to time segment configuration
        analysis_start = _coalesce(
            ("analysis_start", "start_time"), parameters
        )
        analysis_end = _coalesce(
            ("analysis_end", "end_time"), parameters
        )

        time_segment = parameters.get("timeSegment") or parameters.get(
            "time_segment"
        )
        if isinstance(time_segment, dict):
            segment_mode = str(time_segment.get("mode", "")).lower()
            segment_start = _coalesce(
                ("startTime", "start_time"), time_segment
            )
            segment_end = _coalesce(("endTime", "end_time"), time_segment)
            segment_duration = _coalesce(("duration",), time_segment)

            if segment_mode == "entire_file":
                analysis_start = 0 if analysis_start is None else analysis_start
                analysis_end = -1 if analysis_end is None else analysis_end
            elif segment_mode == "start_end_times":
                if segment_start is not None and segment_end is not None:
                    analysis_start = segment_start
                    analysis_end = segment_end
            elif segment_mode == "start_duration":
                if segment_start is not None and segment_duration is not None:
                    analysis_start = segment_start
                    analysis_end = (
                        float(segment_start) + float(segment_duration)
                    )

        resolved_start = _to_number(analysis_start, 0.0, float)
        resolved_end = _to_number(analysis_end, -1.0, float)

        # Frequency band
        low_cutoff = _to_number(
            _coalesce(
                (
                    "low_cutoff",
                    "lowCutoff",
                    "low_frequency",
                    "lowFrequency",
                ),
                frequency_params,
                parameters,
            ),
            50.0,
            float,
        )
        high_cutoff = _to_number(
            _coalesce(
                (
                    "high_cutoff",
                    "highCutoff",
                    "high_frequency",
                    "highFrequency",
                ),
                frequency_params,
                parameters,
            ),
            300.0,
            float,
        )

        # Threshold configuration
        amplitude_1 = _to_number(
            _coalesce(
                (
                    "amplitude_1",
                    "amplitude1",
                    "energy_threshold",
                    "threshold_option1",
                ),
                thresholds_params,
                parameters,
            ),
            2.0,
            float,
        )
        amplitude_2 = _to_number(
            _coalesce(
                (
                    "amplitude_2",
                    "amplitude2",
                    "baseline_threshold",
                    "threshold_option2",
                ),
                thresholds_params,
                parameters,
            ),
            2.0,
            float,
        )
        peaks_1 = _to_number(
            _coalesce(
                ("peaks_1", "peaks1", "min_peaks", "threshold_option3"),
                thresholds_params,
                parameters,
            ),
            6,
            int,
        )
        peaks_2 = _to_number(
            _coalesce(
                ("peaks_2", "peaks2", "threshold_option4"),
                thresholds_params,
                parameters,
            ),
            3,
            int,
        )
        duration = _to_number(
            _coalesce(
                ("duration", "min_duration_ms", "threshold_option5"),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )
        temporal_sync = _to_number(
            _coalesce(
                (
                    "temporal_sync",
                    "temporalSync",
                    "threshold_option6",
                ),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )
        spatial_sync = _to_number(
            _coalesce(
                (
                    "spatial_sync",
                    "spatialSync",
                    "threshold_option7",
                ),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )

        # Normalize montage type from UI ('bipolar'|'average'|'referential')
        ui_montage = _coalesce(
            ("type", "montage", "montage_type"),
            montage_params,
            parameters,
            default="bipolar",
        )
        montage_map = {
            "bipolar": "Bipolar",
            "average": "Average",
            "referential": "Referential",
        }
        normalized_montage = montage_map.get(
            str(ui_montage).lower(), "Bipolar"
        )

        user_ref = _coalesce(
            ("reference", "reference_channel", "user_ref"),
            montage_params,
            parameters,
        )

        return {
            "analysis_start": resolved_start,
            "analysis_end": resolved_end,
            "montage": normalized_montage,
            "user_ref": user_ref,
            "low_cutoff": low_cutoff,
            "high_cutoff": high_cutoff,
            "amplitude_1": amplitude_1,
            "amplitude_2": amplitude_2,
            "peaks_1": peaks_1,
            "peaks_2": peaks_2,
            "duration": duration,
            "temporal_sync": temporal_sync,
            "spatial_sync": spatial_sync,
        }

    def _build_parameter_snapshot(
        self,
        analysis_params: Dict,
        original_parameters: Optional[Dict],
    ) -> Dict:
        """Create a serializable snapshot of the parameters applied to a job."""
        snapshot = {
            "analysis_window_seconds": {
                "start": analysis_params.get("analysis_start"),
                "end": analysis_params.get("analysis_end"),
            },
            "frequency_hz": {
                "low_cutoff": analysis_params.get("low_cutoff"),
                "high_cutoff": analysis_params.get("high_cutoff"),
            },
            "thresholds": {
                "amplitude_1": analysis_params.get("amplitude_1"),
                "amplitude_2": analysis_params.get("amplitude_2"),
                "peaks_1": analysis_params.get("peaks_1"),
                "peaks_2": analysis_params.get("peaks_2"),
                "duration_ms": analysis_params.get("duration"),
                "temporal_sync_ms": analysis_params.get("temporal_sync"),
                "spatial_sync_ms": analysis_params.get("spatial_sync"),
            },
            "montage": analysis_params.get("montage"),
        }

        if analysis_params.get("user_ref"):
            snapshot["reference_channel"] = analysis_params.get("user_ref")

        if isinstance(original_parameters, dict):
            channel_selection = original_parameters.get(
                "channelSelection"
            ) or original_parameters.get("channel_selection")
            if isinstance(channel_selection, dict):
                selected_channels = channel_selection.get(
                    "selectedChannels"
                ) or channel_selection.get("selected_channels")
                if selected_channels:
                    snapshot["channel_selection"] = selected_channels

        return snapshot

    def update_job_status(
        self, job_id: str, status: str, attributes: Optional[Dict] = None
    ):
        """
        Update job status in DynamoDB

        Args:
            job_id: Job identifier
            status: New status
            attributes: Additional attributes to update
        """
        self.aws_service.update_job_status(job_id, status, attributes)

    def cleanup_local_files(self, job_id: str):
        """
        Clean up local temporary files

        Args:
            job_id: Job identifier
        """
        try:
            local_path = os.path.join(self.temp_dir, f"{job_id}.edf")
            if os.path.exists(local_path):
                os.remove(local_path)
                logger.info(f"Cleaned up local file for job {job_id}")
        except Exception as e:
            logger.warning(
                f"Failed to cleanup local files for job {job_id}: {e}")
