"""
Notification Service
Handles email notifications for HFO processing completion and errors
"""

import os
import logging
from typing import Dict, Optional, Any
from datetime import datetime
from .aws_service import AWSService

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for handling email notifications"""

    def __init__(self, aws_service: Optional[AWSService] = None):
        """
        Initialize notification service

        Args:
            aws_service: AWS service instance (optional, creates new if not provided)
        """
        self.aws_service = aws_service or AWSService()
        self.sender_email = os.getenv("SES_SENDER_EMAIL", "<EMAIL>")
        self.frontend_url = os.getenv("FRONTEND_URL", "https://biormika.com")

    def send_completion_email(
        self,
        job_id: str,
        user_email: str,
        file_key: str,
        results: Dict[str, Any],
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Send analysis completion email

        Args:
            job_id: Job identifier
            user_email: Recipient email address
            file_key: S3 key of the analyzed file
            results: Analysis results dictionary

        Returns:
            True if email sent successfully
        """
        try:
            filename = os.path.basename(file_key)
            results_url = f"{self.frontend_url}/analysis/results/{job_id}"
            download_url = f"{self.frontend_url}/api/v1/analysis/download/{job_id}"

            # Extract statistics
            statistics = results.get("statistics", {})
            metadata = results.get("metadata", {})

            template_data = {
                "job_id": job_id,
                "filename": filename,
                "hfo_count": str(statistics.get("total_hfos", 0)),
                "channel_count": str(len(metadata.get("channels", []))),
                "processing_time": str(metadata.get("processing_time", 0)),
                "hfo_density": str(statistics.get("hfo_density", 0)),
                "results_url": results_url,
                "download_url": download_url,
                "completed_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            success = self.aws_service.send_templated_email(
                to_addresses=[user_email],
                template="biormika-analysis-complete",
                template_data=template_data,
                sender_email=sender_email or self.sender_email
            )

            if success:
                logger.info(f"Completion email sent to {user_email} for job {job_id}")
            else:
                logger.error(f"Failed to send completion email for job {job_id}")

            return success

        except Exception as e:
            logger.error(f"Error sending completion email: {e}")
            return False

    def send_error_email(
        self,
        job_id: str,
        user_email: str,
        file_key: str,
        error_message: str,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Send analysis error email

        Args:
            job_id: Job identifier
            user_email: Recipient email address
            file_key: S3 key of the file
            error_message: Error description

        Returns:
            True if email sent successfully
        """
        try:
            filename = os.path.basename(file_key)

            template_data = {
                "job_id": job_id,
                "filename": filename,
                "error_message": error_message,
                "failed_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            success = self.aws_service.send_templated_email(
                to_addresses=[user_email],
                template="biormika-analysis-error",
                template_data=template_data,
                sender_email=sender_email or self.sender_email
            )

            if success:
                logger.info(f"Error email sent to {user_email} for job {job_id}")
            else:
                logger.error(f"Failed to send error email for job {job_id}")

            return success

        except Exception as e:
            logger.error(f"Error sending error email: {e}")
            return False

    def send_status_update_email(
        self,
        job_id: str,
        user_email: str,
        status: str,
        message: Optional[str] = None,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Send job status update email

        Args:
            job_id: Job identifier
            user_email: Recipient email address
            status: Current job status
            message: Optional status message

        Returns:
            True if email sent successfully
        """
        try:
            template_data = {
                "job_id": job_id,
                "status": status,
                "message": message or f"Your job status has been updated to: {status}",
                "updated_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            success = self.aws_service.send_templated_email(
                to_addresses=[user_email],
                template="biormika-status-update",
                template_data=template_data,
                sender_email=sender_email or self.sender_email
            )

            if success:
                logger.info(f"Status update email sent to {user_email} for job {job_id}")
            else:
                logger.error(f"Failed to send status update email for job {job_id}")

            return success

        except Exception as e:
            logger.error(f"Error sending status update email: {e}")
            return False

    def format_results_summary(self, results: Dict[str, Any]) -> str:
        """
        Format results summary for email

        Args:
            results: Analysis results dictionary

        Returns:
            Formatted summary string
        """
        statistics = results.get("statistics", {})
        metadata = results.get("metadata", {})

        summary_lines = [
            "HFO Analysis Summary:",
            f"- Total HFOs detected: {statistics.get('total_hfos', 0)}",
            f"- HFO density: {statistics.get('hfo_density', 0):.2f} per minute",
            f"- Channels analyzed: {len(metadata.get('channels', []))}",
            f"- Duration analyzed: {metadata.get('duration_seconds', 0):.2f} seconds",
        ]

        # Add channel-specific information if available
        if "channels_with_hfos" in statistics:
            summary_lines.append(
                f"- Channels with HFOs: {len(statistics['channels_with_hfos'])}"
            )

        return "\n".join(summary_lines)

    def format_error_details(self, error: str, job_id: str) -> str:
        """
        Format error details for email

        Args:
            error: Error message
            job_id: Job identifier

        Returns:
            Formatted error details
        """
        error_lines = [
            "Analysis Error Details:",
            f"- Job ID: {job_id}",
            f"- Error: {error}",
            "",
            "Possible causes:",
        ]

        # Add specific error guidance based on error type
        if "sampling rate" in error.lower():
            error_lines.append("- The EDF file has an incompatible sampling rate")
            error_lines.append("- Minimum sampling rate required: 200 Hz")
        elif "file" in error.lower():
            error_lines.append("- The EDF file may be corrupted or invalid")
            error_lines.append("- Please verify the file can be opened in an EDF viewer")
        elif "memory" in error.lower():
            error_lines.append("- The file is too large to process")
            error_lines.append("- Maximum file size: 2 GB")
        else:
            error_lines.append("- An unexpected error occurred during processing")
            error_lines.append("- Please contact support if this persists")

        return "\n".join(error_lines)
