#!/bin/bash

set -e

# Configuration
AWS_PROFILE="biormika"
AWS_REGION="us-east-1"
AWS_ACCOUNT_ID=$(aws sts get-caller-identity --profile $AWS_PROFILE --query Account --output text)
ECR_REPOSITORY="biormika-hfo-processor"
IMAGE_TAG="latest"

echo "Building HFO Processor Docker Image"
echo "===================================="
echo "AWS Account: $AWS_ACCOUNT_ID"
echo "Region: $AWS_REGION"
echo "Repository: $ECR_REPOSITORY"
echo ""

# Create ECR repository if it doesn't exist
echo "Ensuring ECR repository exists..."
aws ecr describe-repositories --repository-names $ECR_REPOSITORY --profile $AWS_PROFILE --region $AWS_REGION 2>/dev/null || \
    aws ecr create-repository --repository-name $ECR_REPOSITORY --profile $AWS_PROFILE --region $AWS_REGION

# Get login token for ECR
echo "Logging into ECR..."
aws ecr get-login-password --region $AWS_REGION --profile $AWS_PROFILE | \
    docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# Copy HFO core from biormika-http-backend (if available)
if [ -d "../../biormika-http-backend/backend/core" ]; then
    echo "Copying HFO core modules..."
    cp -r ../../biormika-http-backend/backend/core ./core
    cp -r ../../biormika-http-backend/backend/browse_files.py ./utils/ 2>/dev/null || true
else
    echo "Warning: biormika-http-backend not found. Creating placeholder core module..."
    mkdir -p core
    echo "# Placeholder for HFO engine" > core/__init__.py
fi

# Build Docker image
echo "Building Docker image..."
docker build -t $ECR_REPOSITORY:$IMAGE_TAG .

# Tag for ECR
docker tag $ECR_REPOSITORY:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG

# Push to ECR
echo "Pushing to ECR..."
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG

echo ""
echo "✅ Docker image built and pushed successfully!"
echo "Image URI: $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:$IMAGE_TAG"