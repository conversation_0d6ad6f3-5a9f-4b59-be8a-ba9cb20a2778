"""
Signal processing utility functions for HFO detection.
Extracted from hfo_analysis.py for better modularity.
"""

import numpy as np
from scipy import signal
from typing import Tuple, List, Optional, Union

def calculate_signal_energy(data: np.ndarray, window_size: int = None) -> np.ndarray:
    """
    Calculate signal energy using sliding window.
    
    Args:
        data: Input signal data
        window_size: Size of sliding window (optional)
        
    Returns:
        Energy signal
    """
    if window_size is None:
        return np.square(data)
    
    # Apply sliding window for energy calculation
    energy = np.convolve(np.square(data), np.ones(window_size)/window_size, mode='same')
    return energy

def apply_hilbert_transform(data: np.ndarray) -> np.ndarray:
    """
    Apply Hilbert transform to get analytic signal.
    
    Args:
        data: Input signal
        
    Returns:
        Analytic signal envelope
    """
    analytic_signal = signal.hilbert(data)
    envelope = np.abs(analytic_signal)
    return envelope

def detect_peaks(data: np.ndarray, threshold: float, 
                min_distance: int = 1) -> np.ndarray:
    """
    Detect peaks in signal above threshold.
    
    Args:
        data: Input signal
        threshold: Peak detection threshold
        min_distance: Minimum distance between peaks
        
    Returns:
        Indices of detected peaks
    """
    peaks, _ = signal.find_peaks(data, height=threshold, distance=min_distance)
    return peaks

def calculate_instantaneous_frequency(analytic_signal: np.ndarray, 
                                     sampling_rate: float) -> np.ndarray:
    """
    Calculate instantaneous frequency from analytic signal.
    
    Args:
        analytic_signal: Complex analytic signal
        sampling_rate: Sampling frequency in Hz
        
    Returns:
        Instantaneous frequency array
    """
    instantaneous_phase = np.unwrap(np.angle(analytic_signal))
    instantaneous_frequency = (np.diff(instantaneous_phase) / 
                              (2.0 * np.pi) * sampling_rate)
    return instantaneous_frequency

def calculate_power_spectrum(data: np.ndarray, sampling_rate: float,
                            nperseg: int = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    Calculate power spectral density using Welch's method.
    
    Args:
        data: Input signal
        sampling_rate: Sampling frequency in Hz
        nperseg: Length of each segment for Welch's method
        
    Returns:
        Tuple of (frequencies, power spectral density)
    """
    if nperseg is None:
        nperseg = min(256, len(data))
    
    frequencies, psd = signal.welch(data, sampling_rate, nperseg=nperseg)
    return frequencies, psd

def remove_baseline(data: np.ndarray, baseline_window: int = None) -> np.ndarray:
    """
    Remove baseline from signal using moving average.
    
    Args:
        data: Input signal
        baseline_window: Window size for baseline estimation
        
    Returns:
        Signal with baseline removed
    """
    if baseline_window is None:
        baseline = np.mean(data)
    else:
        baseline = np.convolve(data, np.ones(baseline_window)/baseline_window, mode='same')
    
    return data - baseline

def calculate_rms(data: np.ndarray) -> float:
    """
    Calculate root mean square of signal.
    
    Args:
        data: Input signal
        
    Returns:
        RMS value
    """
    return np.sqrt(np.mean(np.square(data)))

def zero_crossing_rate(data: np.ndarray) -> float:
    """
    Calculate zero crossing rate of signal.
    
    Args:
        data: Input signal
        
    Returns:
        Zero crossing rate
    """
    zero_crossings = np.where(np.diff(np.signbit(data)))[0]
    return len(zero_crossings) / len(data)

def apply_window(data: np.ndarray, window_type: str = 'hann') -> np.ndarray:
    """
    Apply window function to signal.
    
    Args:
        data: Input signal
        window_type: Type of window ('hann', 'hamming', 'blackman', etc.)
        
    Returns:
        Windowed signal
    """
    window = signal.get_window(window_type, len(data))
    return data * window