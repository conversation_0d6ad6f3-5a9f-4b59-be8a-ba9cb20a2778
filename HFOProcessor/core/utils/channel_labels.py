"""
Utility functions for parsing and processing channel labels.
Consolidates duplicate channel label processing logic.
"""

import re
from typing import Dict, List, Tuple, Optional, Set


class ChannelLabelUtils:
    """Utilities for parsing and processing EEG channel labels"""
    
    # Common patterns used across the codebase
    BIPOLAR_PATTERN = re.compile(r'(\w+)(\d+)-(\w+)(\d+)')
    POL_PREFIX_PATTERN = re.compile(r'^POL\s+', re.IGNORECASE)
    P_PREFIX_PATTERN = re.compile(r'^P\s+', re.IGNORECASE)
    CHANNEL_NUMBER_PATTERN = re.compile(r'(\d+)')
    
    @staticmethod
    def clean_channel_label(label: str) -> str:
        """
        Clean channel label by removing common prefixes.
        
        Removes prefixes like 'POL ', 'P ', etc.
        
        Args:
            label: Raw channel label
            
        Returns:
            str: Cleaned channel label
        """
        # Remove 'POL ' prefix (case insensitive)
        label = ChannelLabelUtils.POL_PREFIX_PATTERN.sub('', label)
        # Remove 'P ' prefix (case insensitive)
        label = ChannelLabelUtils.P_PREFIX_PATTERN.sub('', label)
        # Strip whitespace
        return label.strip()
    
    @staticmethod
    def parse_bipolar_label(label: str) -> Optional[Tuple[str, int, str, int]]:
        """
        Parse a bipolar channel label (e.g., 'FP1-FP2').
        
        Args:
            label: Channel label to parse
            
        Returns:
            Tuple of (lead1, num1, lead2, num2) if bipolar format
            None if not bipolar format
        """
        match = ChannelLabelUtils.BIPOLAR_PATTERN.match(label)
        if match:
            lead1, num1, lead2, num2 = match.groups()
            return (lead1, int(num1), lead2, int(num2))
        return None
    
    @staticmethod
    def extract_channel_number(label: str) -> Optional[int]:
        """
        Extract channel number from a label.
        
        Args:
            label: Channel label
            
        Returns:
            int: Channel number if found
            None: If no number found
        """
        match = ChannelLabelUtils.CHANNEL_NUMBER_PATTERN.search(label)
        if match:
            return int(match.group(1))
        return None
    
    @staticmethod
    def extract_channel_groups(labels: List[str]) -> Dict[str, List[int]]:
        """
        Extract channel groups from a list of labels.
        
        Groups channels by their lead name (e.g., 'FP', 'F', 'C', etc.)
        This consolidates logic from app.py (_extract_channel_groups).
        
        Args:
            labels: List of channel labels
            
        Returns:
            Dict mapping lead names to lists of channel indices
        """
        groups = {}
        
        for idx, label in enumerate(labels):
            # Clean the label
            clean_label = ChannelLabelUtils.clean_channel_label(label)
            
            # Try to parse as bipolar
            bipolar = ChannelLabelUtils.parse_bipolar_label(clean_label)
            if bipolar:
                lead1, num1, lead2, num2 = bipolar
                # Add both leads to groups
                if lead1 not in groups:
                    groups[lead1] = []
                if idx not in groups[lead1]:
                    groups[lead1].append(idx)
                continue
            
            # Extract lead name (letters) and number
            # Match pattern like 'FP1', 'C3', etc.
            match = re.match(r'([A-Za-z]+)(\d+)', clean_label)
            if match:
                lead_name = match.group(1).upper()
                if lead_name not in groups:
                    groups[lead_name] = []
                groups[lead_name].append(idx)
            else:
                # If no pattern matches, use the whole label as group
                if clean_label and clean_label not in groups:
                    groups[clean_label] = []
                if clean_label:
                    groups[clean_label].append(idx)
        
        return groups
    
    @staticmethod
    def parse_contact_specification(spec: str) -> List[int]:
        """
        Parse contact specification string (e.g., "1-5", "1,3,5", "1-3,5,7-9").
        
        Args:
            spec: Contact specification string
            
        Returns:
            List of contact numbers
            
        Raises:
            ValueError: If specification is invalid
        """
        if not spec or spec.strip() == '':
            return []
        
        contacts = []
        parts = spec.split(',')
        
        for part in parts:
            part = part.strip()
            if '-' in part:
                # Range specification
                try:
                    start, end = part.split('-')
                    start_num = int(start.strip())
                    end_num = int(end.strip())
                    if start_num > end_num:
                        raise ValueError(f"Invalid range: {start_num}-{end_num}")
                    contacts.extend(range(start_num, end_num + 1))
                except (ValueError, AttributeError) as e:
                    raise ValueError(f"Invalid range specification: {part}")
            else:
                # Single number
                try:
                    contacts.append(int(part))
                except ValueError:
                    raise ValueError(f"Invalid contact number: {part}")
        
        # Remove duplicates and sort
        return sorted(list(set(contacts)))
    
    @staticmethod
    def get_unique_lead_names(labels: List[str]) -> Set[str]:
        """
        Extract unique lead names from channel labels.
        
        Args:
            labels: List of channel labels
            
        Returns:
            Set of unique lead names
        """
        lead_names = set()
        
        for label in labels:
            clean_label = ChannelLabelUtils.clean_channel_label(label)
            
            # Try bipolar format
            bipolar = ChannelLabelUtils.parse_bipolar_label(clean_label)
            if bipolar:
                lead_names.add(bipolar[0])
                lead_names.add(bipolar[2])
                continue
            
            # Extract lead name from standard format
            match = re.match(r'([A-Za-z]+)\d+', clean_label)
            if match:
                lead_names.add(match.group(1).upper())
        
        return lead_names
    
    @staticmethod
    def format_bipolar_label(lead1: str, num1: int, lead2: str, num2: int) -> str:
        """
        Format a bipolar channel label.
        
        Args:
            lead1: First lead name
            num1: First channel number
            lead2: Second lead name
            num2: Second channel number
            
        Returns:
            str: Formatted bipolar label (e.g., 'FP1-FP2')
        """
        return f"{lead1}{num1}-{lead2}{num2}"
    
    @staticmethod
    def is_valid_channel_label(label: str) -> bool:
        """
        Check if a channel label follows expected formats.
        
        Args:
            label: Channel label to validate
            
        Returns:
            bool: True if valid format
        """
        if not label or not isinstance(label, str):
            return False
        
        clean_label = ChannelLabelUtils.clean_channel_label(label)
        
        # Check for bipolar format
        if ChannelLabelUtils.parse_bipolar_label(clean_label):
            return True
        
        # Check for standard format (letters followed by numbers)
        if re.match(r'^[A-Za-z]+\d+$', clean_label):
            return True
        
        # Check if it's a valid lead name without numbers
        if re.match(r'^[A-Za-z]+$', clean_label):
            return True
        
        return False