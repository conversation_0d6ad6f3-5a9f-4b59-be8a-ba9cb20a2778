"""
HFO validation functions for detection algorithm.
Extracted from hfo_analysis.py for better modularity.
"""

import numpy as np
import logging
from typing import Tuple, List, Optional, Dict, Any

logger = logging.getLogger(__name__)

class HFOValidator:
    """Validator for HFO detection criteria"""
    
    def __init__(self, threshold_params: Dict[str, float]):
        """
        Initialize HFO validator with threshold parameters.
        
        Args:
            threshold_params: Dictionary containing:
                - amplitude1: HFO amplitude >= energy signal (times std)
                - amplitude2: HFO amplitude >= baseline (times std)
                - peaks1: Number of peaks >= amplitude1
                - peaks2: Number of peaks >= amplitude2
                - duration: Minimum HFO length in ms
                - temporal_sync: Inter-HFO interval in channel (ms)
                - spatial_sync: Inter-HFO interval across channels (ms)
        """
        self.amplitude1_thresh = threshold_params.get('amplitude1', 2)
        self.amplitude2_thresh = threshold_params.get('amplitude2', 2)
        self.peaks1_thresh = threshold_params.get('peaks1', 6)
        self.peaks2_thresh = threshold_params.get('peaks2', 3)
        self.duration_thresh = threshold_params.get('duration', 10)
        self.temporal_sync = threshold_params.get('temporal_sync', 10)
        self.spatial_sync = threshold_params.get('spatial_sync', 10)
    
    def validate_hfo_event(self, hfo_segment: np.ndarray, 
                          energy_signal: np.ndarray,
                          baseline: float, 
                          sampling_rate: float) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate if a segment contains a valid HFO.
        
        Args:
            hfo_segment: Potential HFO signal segment
            energy_signal: Energy signal for the segment
            baseline: Baseline value for comparison
            sampling_rate: Sampling frequency in Hz
            
        Returns:
            Tuple of (is_valid, characteristics_dict)
        """
        characteristics = {}
        
        # Calculate duration
        duration_ms = (len(hfo_segment) / sampling_rate) * 1000
        characteristics['duration_ms'] = duration_ms
        
        # Check minimum duration
        if duration_ms < self.duration_thresh:
            return False, characteristics
        
        # Calculate amplitudes
        mean_energy = np.mean(energy_signal)
        std_energy = np.std(energy_signal)
        
        # Count peaks above thresholds
        threshold1 = mean_energy + self.amplitude1_thresh * std_energy
        threshold2 = baseline + self.amplitude2_thresh * np.std(hfo_segment)
        
        peaks1 = np.sum(hfo_segment >= threshold1)
        peaks2 = np.sum(hfo_segment >= threshold2)
        
        characteristics['peaks_above_threshold1'] = peaks1
        characteristics['peaks_above_threshold2'] = peaks2
        
        # Validate peak counts
        if peaks1 < self.peaks1_thresh or peaks2 < self.peaks2_thresh:
            return False, characteristics
        
        return True, characteristics
    
    def check_temporal_proximity(self, hfo_times: List[float], 
                                new_hfo_time: float) -> bool:
        """
        Check if new HFO is too close to existing HFOs in same channel.
        
        Args:
            hfo_times: List of existing HFO times in channel
            new_hfo_time: Time of new potential HFO
            
        Returns:
            True if HFO is valid (not too close), False otherwise
        """
        if not hfo_times:
            return True
        
        min_distance = min(abs(new_hfo_time - t) for t in hfo_times)
        return min_distance >= (self.temporal_sync / 1000)  # Convert ms to seconds
    
    def check_spatial_proximity(self, channel_hfos: Dict[str, List[float]], 
                               current_channel: str,
                               new_hfo_time: float) -> bool:
        """
        Check if new HFO is too close to HFOs in other channels.
        
        Args:
            channel_hfos: Dictionary of HFO times per channel
            current_channel: Current channel being processed
            new_hfo_time: Time of new potential HFO
            
        Returns:
            True if HFO is valid (not too close), False otherwise
        """
        for channel, hfo_times in channel_hfos.items():
            if channel == current_channel:
                continue
            
            for hfo_time in hfo_times:
                if abs(new_hfo_time - hfo_time) < (self.spatial_sync / 1000):
                    return False
        
        return True
    
    def validate_frequency_content(self, hfo_segment: np.ndarray,
                                  sampling_rate: float,
                                  min_freq: float,
                                  max_freq: float) -> bool:
        """
        Validate that HFO has appropriate frequency content.
        
        Args:
            hfo_segment: HFO signal segment
            sampling_rate: Sampling frequency in Hz
            min_freq: Minimum expected HFO frequency
            max_freq: Maximum expected HFO frequency
            
        Returns:
            True if frequency content is valid
        """
        # Compute FFT
        fft_result = np.fft.fft(hfo_segment)
        frequencies = np.fft.fftfreq(len(hfo_segment), 1/sampling_rate)
        
        # Get power in HFO frequency band
        freq_mask = (frequencies >= min_freq) & (frequencies <= max_freq)
        hfo_power = np.sum(np.abs(fft_result[freq_mask])**2)
        
        # Get total power
        total_power = np.sum(np.abs(fft_result)**2)
        
        # Check if majority of power is in HFO band
        if total_power > 0:
            hfo_power_ratio = hfo_power / total_power
            return hfo_power_ratio > 0.5  # At least 50% of power in HFO band
        
        return False
    
    def classify_hfo_type(self, peak_frequency: float) -> str:
        """
        Classify HFO type based on peak frequency.
        
        Args:
            peak_frequency: Peak frequency of the HFO
            
        Returns:
            HFO type string ('ripple', 'fast_ripple', or 'unknown')
        """
        if 80 <= peak_frequency < 250:
            return 'ripple'
        elif 250 <= peak_frequency <= 500:
            return 'fast_ripple'
        else:
            return 'unknown'
    
    def calculate_hfo_score(self, characteristics: Dict[str, Any]) -> float:
        """
        Calculate a confidence score for the HFO detection.
        
        Args:
            characteristics: Dictionary of HFO characteristics
            
        Returns:
            Confidence score between 0 and 1
        """
        score = 0.0
        max_score = 5.0
        
        # Duration score (longer is better within reasonable range)
        duration = characteristics.get('duration_ms', 0)
        if 10 <= duration <= 100:
            score += 1.0
        elif 5 <= duration < 10 or 100 < duration <= 200:
            score += 0.5
        
        # Peak count score
        peaks1 = characteristics.get('peaks_above_threshold1', 0)
        if peaks1 >= self.peaks1_thresh * 1.5:
            score += 1.0
        elif peaks1 >= self.peaks1_thresh:
            score += 0.5
        
        peaks2 = characteristics.get('peaks_above_threshold2', 0)
        if peaks2 >= self.peaks2_thresh * 1.5:
            score += 1.0
        elif peaks2 >= self.peaks2_thresh:
            score += 0.5
        
        # Frequency content score
        if 'peak_frequency' in characteristics:
            peak_freq = characteristics['peak_frequency']
            if 80 <= peak_freq <= 500:
                score += 1.0
            elif 70 <= peak_freq < 80 or 500 < peak_freq <= 600:
                score += 0.5
        
        # Power score
        if 'power' in characteristics:
            score += min(1.0, characteristics['power'] / 1000)
        
        return min(1.0, score / max_score)