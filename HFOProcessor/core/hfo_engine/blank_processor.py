"""
Blank and Discontinuity Processor
Handles detection and processing of signal blanks and discontinuities
"""

import numpy as np
import logging
from typing import List, Tuple, Optional, Dict, Any

from core.constants import (
    BLANK_DATA_MARKER,
    DISCONTINUITY_LENGTH_MS,
    MIN_BLANK_DURATION_POINTS,
    BLANK_PROXIMITY_MARGIN
)

logger = logging.getLogger(__name__)


class BlankProcessor:
    """Processes blank periods and discontinuities in EEG signals"""

    def __init__(self, sampling_rate: float):
        """
        Initialize blank processor

        Args:
            sampling_rate: Sampling frequency in Hz
        """
        self.sampling_rate = sampling_rate
        self.samples_per_ms = sampling_rate / 1000

    def find_blanks(self, signal: np.ndarray,
                   channel_labels: List[str]) -> Tuple[List[int], List[int], List[str]]:
        """
        Find discontinuities or blank periods in EEG signal

        Args:
            signal: EEG signal matrix (channels x samples)
            channel_labels: List of channel labels

        Returns:
            Tuple of (blank_starts, blank_ends, artifact_channels)
        """
        num_channels, num_samples = signal.shape

        # Collect blanks for each channel
        channel_blanks = []

        for ch in range(num_channels):
            channel_data = signal[ch, :]

            # Find consecutive identical values
            blank_starts = []
            blank_ends = []
            in_blank = False
            blank_start = 0

            for i in range(num_samples - 2):
                # Check for 3 identical consecutive points
                if channel_data[i] == channel_data[i + 1] == channel_data[i + 2]:
                    if not in_blank:
                        in_blank = True
                        blank_start = i
                else:
                    if in_blank:
                        in_blank = False
                        # Only keep blanks longer than minimum duration
                        if i + 1 - blank_start >= MIN_BLANK_DURATION_POINTS:
                            blank_starts.append(blank_start)
                            blank_ends.append(i + 1)

            # Handle blank extending to end
            if in_blank and num_samples - blank_start >= MIN_BLANK_DURATION_POINTS:
                blank_starts.append(blank_start)
                blank_ends.append(num_samples)

            channel_blanks.append((blank_starts, blank_ends))

        # Determine true blanks (present in majority of channels)
        blank_counts = [len(blanks[0]) for blanks in channel_blanks]
        true_blank_count = int(np.median(blank_counts)) if blank_counts else 0

        # Identify artifact channels (different number of blanks)
        artifact_channels = []
        for ch, count in enumerate(blank_counts):
            if count != true_blank_count:
                artifact_channels.append(channel_labels[ch])

        # Get true blank periods from first valid channel
        true_blank_starts = []
        true_blank_ends = []
        for ch, count in enumerate(blank_counts):
            if count == true_blank_count and channel_blanks[ch][0]:
                true_blank_starts = channel_blanks[ch][0]
                true_blank_ends = channel_blanks[ch][1]
                break

        return true_blank_starts, true_blank_ends, artifact_channels

    def add_visual_discontinuities(self,
                                  blank_starts: List[int],
                                  blank_ends: List[int],
                                  visual_discontinuities: List[float],
                                  discontinuity_length_ms: float = DISCONTINUITY_LENGTH_MS
                                  ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Add user-defined visual discontinuities to blank periods

        Args:
            blank_starts: Existing blank start indices
            blank_ends: Existing blank end indices
            visual_discontinuities: User-defined discontinuity times in seconds
            discontinuity_length_ms: Length of discontinuity in milliseconds

        Returns:
            Tuple of updated (blank_starts, blank_ends) arrays
        """
        if not visual_discontinuities:
            return np.array(blank_starts), np.array(blank_ends)

        # Convert discontinuity times to sample indices
        discontinuity_samples = int(discontinuity_length_ms * self.samples_per_ms)

        visual_starts = []
        visual_ends = []

        for discont_time in visual_discontinuities:
            start_idx = int(discont_time * self.sampling_rate)
            end_idx = start_idx + discontinuity_samples
            visual_starts.append(start_idx)
            visual_ends.append(end_idx)

        # Combine with existing blanks
        all_starts = np.concatenate([blank_starts, visual_starts])
        all_ends = np.concatenate([blank_ends, visual_ends])

        # Sort by start time
        sort_idx = np.argsort(all_starts)
        all_starts = all_starts[sort_idx]
        all_ends = all_ends[sort_idx]

        return all_starts, all_ends

    def merge_overlapping_blanks(self,
                                blank_starts: np.ndarray,
                                blank_ends: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Merge overlapping or adjacent blank periods

        Args:
            blank_starts: Array of blank start indices
            blank_ends: Array of blank end indices

        Returns:
            Tuple of merged (blank_starts, blank_ends)
        """
        if len(blank_starts) == 0:
            return blank_starts, blank_ends

        merged_starts = []
        merged_ends = []

        current_start = blank_starts[0]
        current_end = blank_ends[0]

        for start, end in zip(blank_starts[1:], blank_ends[1:]):
            if start <= current_end:
                # Overlapping or adjacent - merge
                current_end = max(current_end, end)
            else:
                # Non-overlapping - save current and start new
                merged_starts.append(current_start)
                merged_ends.append(current_end)
                current_start = start
                current_end = end

        # Add the last blank
        merged_starts.append(current_start)
        merged_ends.append(current_end)

        return np.array(merged_starts), np.array(merged_ends)

    def remove_blanks_from_signal(self,
                                 signal: np.ndarray,
                                 blank_starts: np.ndarray,
                                 blank_ends: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove blank periods from the signal

        Args:
            signal: EEG signal matrix (channels x samples)
            blank_starts: Start indices of blanks
            blank_ends: End indices of blanks

        Returns:
            Tuple of (cleaned_signal, time_mapping)
        """
        if len(blank_starts) == 0:
            return signal, np.arange(signal.shape[1])

        num_channels = signal.shape[0]

        # Create mask for valid samples
        valid_mask = np.ones(signal.shape[1], dtype=bool)
        for start, end in zip(blank_starts, blank_ends):
            valid_mask[int(start):int(end)] = False

        # Extract valid samples
        cleaned_signal = signal[:, valid_mask]

        # Create time mapping (original index -> new index)
        time_mapping = np.where(valid_mask)[0]

        return cleaned_signal, time_mapping

    def mark_blanks_in_signal(self,
                             signal: np.ndarray,
                             blank_starts: np.ndarray,
                             blank_ends: np.ndarray,
                             marker_value: float = BLANK_DATA_MARKER) -> np.ndarray:
        """
        Mark blank periods in signal with a special value

        Args:
            signal: EEG signal matrix
            blank_starts: Start indices of blanks
            blank_ends: End indices of blanks
            marker_value: Value to mark blanks with

        Returns:
            Signal with marked blanks
        """
        marked_signal = signal.copy()

        for start, end in zip(blank_starts, blank_ends):
            marked_signal[:, int(start):int(end)] = marker_value

        return marked_signal

    def check_proximity_to_blanks(self,
                                 event_start: int,
                                 event_end: int,
                                 blank_starts: np.ndarray,
                                 blank_ends: np.ndarray,
                                 margin: int = BLANK_PROXIMITY_MARGIN) -> bool:
        """
        Check if an event is near any blank period

        Args:
            event_start: Start index of event
            event_end: End index of event
            blank_starts: Start indices of blanks
            blank_ends: End indices of blanks
            margin: Proximity margin in samples

        Returns:
            True if event is near a blank
        """
        for blank_start, blank_end in zip(blank_starts, blank_ends):
            if (event_end >= blank_start - margin and
                event_start <= blank_end + margin):
                return True

        return False

    def get_blank_statistics(self,
                            blank_starts: np.ndarray,
                            blank_ends: np.ndarray) -> Dict[str, Any]:
        """
        Calculate statistics about blank periods

        Args:
            blank_starts: Start indices of blanks
            blank_ends: End indices of blanks

        Returns:
            Dictionary with blank statistics
        """
        if len(blank_starts) == 0:
            return {
                'num_blanks': 0,
                'total_blank_duration_ms': 0,
                'mean_blank_duration_ms': 0,
                'max_blank_duration_ms': 0,
                'blank_percentage': 0
            }

        # Calculate durations
        durations = blank_ends - blank_starts
        durations_ms = durations / self.samples_per_ms

        # Total signal duration
        if hasattr(self, 'signal_duration_samples'):
            total_duration = self.signal_duration_samples
        else:
            total_duration = np.max(blank_ends)

        total_blank_samples = np.sum(durations)
        blank_percentage = (total_blank_samples / total_duration) * 100

        return {
            'num_blanks': len(blank_starts),
            'total_blank_duration_ms': float(np.sum(durations_ms)),
            'mean_blank_duration_ms': float(np.mean(durations_ms)),
            'max_blank_duration_ms': float(np.max(durations_ms)),
            'min_blank_duration_ms': float(np.min(durations_ms)),
            'blank_percentage': float(blank_percentage),
            'blank_starts': blank_starts.tolist(),
            'blank_ends': blank_ends.tolist()
        }

    def process_blanks_for_analysis(self,
                                   signal: np.ndarray,
                                   channel_labels: List[str],
                                   visual_discontinuities: Optional[List[float]] = None,
                                   remove_discontinuities: bool = True,
                                   gui_output: Optional[callable] = None
                                   ) -> Dict[str, Any]:
        """
        Complete blank processing pipeline

        Args:
            signal: EEG signal matrix
            channel_labels: Channel labels
            visual_discontinuities: User-defined discontinuity times
            remove_discontinuities: Whether to remove blanks from signal
            gui_output: Optional callback for status messages

        Returns:
            Dictionary with processed signal and blank information
        """
        if gui_output:
            gui_output("\nProcessing blanks and discontinuities...")

        # Find automatic blanks
        blank_starts, blank_ends, artifact_channels = self.find_blanks(
            signal, channel_labels
        )

        if gui_output and artifact_channels:
            gui_output(f"Found artifact channels: {', '.join(artifact_channels)}")

        # Add visual discontinuities if provided
        if visual_discontinuities:
            blank_starts, blank_ends = self.add_visual_discontinuities(
                blank_starts, blank_ends, visual_discontinuities
            )

        # Merge overlapping blanks
        blank_starts, blank_ends = self.merge_overlapping_blanks(
            np.array(blank_starts), np.array(blank_ends)
        )

        # Process signal based on settings
        if remove_discontinuities and len(blank_starts) > 0:
            processed_signal, time_mapping = self.remove_blanks_from_signal(
                signal, blank_starts, blank_ends
            )
            if gui_output:
                gui_output(f"Removed {len(blank_starts)} blank periods from signal")
        else:
            processed_signal = signal
            time_mapping = np.arange(signal.shape[1])

        # Calculate statistics
        self.signal_duration_samples = signal.shape[1]
        statistics = self.get_blank_statistics(blank_starts, blank_ends)

        if gui_output:
            gui_output(f"Blank processing complete: {statistics['num_blanks']} blanks found")

        return {
            'processed_signal': processed_signal,
            'blank_starts': blank_starts,
            'blank_ends': blank_ends,
            'artifact_channels': artifact_channels,
            'time_mapping': time_mapping,
            'statistics': statistics
        }