"""
Montage processing functions for EEG data.
Handles bipolar, average, and referential montages.
"""

import numpy as np
from typing import List, Tu<PERSON>, Optional, Dict


def apply_bipolar_montage(data: np.ndarray, 
                         channel_labels: List[str]) -> Tuple[np.ndar<PERSON>, List[str]]:
    """
    Apply bipolar montage (sequential channel subtraction).
    
    Args:
        data: EEG data (channels x samples)
        channel_labels: List of channel labels
        
    Returns:
        Tuple of (montaged data, new channel labels)
    """
    num_channels = data.shape[0]
    
    if num_channels < 2:
        raise ValueError("Bipolar montage requires at least 2 channels")
    
    # Create bipolar montage: each channel minus the next
    bipolar_data = np.zeros((num_channels - 1, data.shape[1]))
    bipolar_labels = []
    
    for i in range(num_channels - 1):
        bipolar_data[i] = data[i] - data[i + 1]
        bipolar_labels.append(f"{channel_labels[i]}-{channel_labels[i + 1]}")
    
    return bipolar_data, bipolar_labels


def apply_average_montage(data: np.ndarray,
                         channel_labels: List[str],
                         exclude_channels: Optional[List[int]] = None) -> Tuple[np.ndarray, List[str]]:
    """
    Apply average reference montage (common average reference).
    
    Args:
        data: EEG data (channels x samples)
        channel_labels: List of channel labels
        exclude_channels: Indices of channels to exclude from average calculation
        
    Returns:
        Tuple of (montaged data, channel labels with '-Avg' suffix)
    """
    if exclude_channels is None:
        exclude_channels = []
    
    # Calculate channels to include in average
    include_mask = np.ones(data.shape[0], dtype=bool)
    include_mask[exclude_channels] = False
    
    # Calculate average reference
    average_ref = np.mean(data[include_mask], axis=0)
    
    # Subtract average from each channel
    avg_montage_data = data - average_ref
    
    # Update labels
    avg_labels = [f"{label}-Avg" for label in channel_labels]
    
    return avg_montage_data, avg_labels


def apply_referential_montage(data: np.ndarray,
                            channel_labels: List[str],
                            reference_channel: int) -> Tuple[np.ndarray, List[str]]:
    """
    Apply referential montage using a specific channel as reference.
    
    Args:
        data: EEG data (channels x samples)
        channel_labels: List of channel labels
        reference_channel: Index of the reference channel
        
    Returns:
        Tuple of (montaged data, new channel labels)
    """
    if reference_channel >= data.shape[0]:
        raise ValueError(f"Reference channel {reference_channel} out of range")
    
    ref_label = channel_labels[reference_channel]
    reference_data = data[reference_channel]
    
    # Subtract reference from all channels
    ref_montage_data = data - reference_data
    
    # Remove reference channel from output
    mask = np.ones(data.shape[0], dtype=bool)
    mask[reference_channel] = False
    ref_montage_data = ref_montage_data[mask]
    
    # Update labels
    ref_labels = [f"{label}-{ref_label}" for i, label in enumerate(channel_labels) if i != reference_channel]
    
    return ref_montage_data, ref_labels


def apply_custom_montage(data: np.ndarray,
                        channel_labels: List[str],
                        montage_matrix: np.ndarray,
                        montage_labels: List[str]) -> Tuple[np.ndarray, List[str]]:
    """
    Apply a custom montage using a transformation matrix.
    
    Args:
        data: EEG data (channels x samples)
        channel_labels: Original channel labels
        montage_matrix: Transformation matrix (new_channels x original_channels)
        montage_labels: Labels for the new montage channels
        
    Returns:
        Tuple of (montaged data, montage labels)
    """
    if montage_matrix.shape[1] != data.shape[0]:
        raise ValueError("Montage matrix columns must match number of channels")
    
    if len(montage_labels) != montage_matrix.shape[0]:
        raise ValueError("Number of montage labels must match montage matrix rows")
    
    # Apply montage transformation
    montaged_data = montage_matrix @ data
    
    return montaged_data, montage_labels


def create_laplacian_montage(data: np.ndarray,
                           channel_labels: List[str],
                           channel_positions: Dict[str, Tuple[float, float]]) -> Tuple[np.ndarray, List[str]]:
    """
    Create a Laplacian montage based on channel positions.
    Each channel is referenced to the average of its nearest neighbors.
    
    Args:
        data: EEG data (channels x samples)
        channel_labels: List of channel labels
        channel_positions: Dictionary mapping channel labels to (x, y) positions
        
    Returns:
        Tuple of (montaged data, new channel labels)
    """
    num_channels = data.shape[0]
    
    # Calculate distances between channels
    distances = np.zeros((num_channels, num_channels))
    for i in range(num_channels):
        for j in range(num_channels):
            if i != j:
                pos_i = channel_positions.get(channel_labels[i], (0, 0))
                pos_j = channel_positions.get(channel_labels[j], (0, 0))
                distances[i, j] = np.sqrt((pos_i[0] - pos_j[0])**2 + (pos_i[1] - pos_j[1])**2)
            else:
                distances[i, j] = np.inf
    
    # Create Laplacian montage
    laplacian_data = np.zeros_like(data)
    laplacian_labels = []
    
    for i in range(num_channels):
        # Find 4 nearest neighbors
        nearest_indices = np.argsort(distances[i])[:4]
        
        # Calculate average of neighbors
        neighbor_avg = np.mean(data[nearest_indices], axis=0)
        
        # Laplacian = channel - average of neighbors
        laplacian_data[i] = data[i] - neighbor_avg
        laplacian_labels.append(f"{channel_labels[i]}-Lap")
    
    return laplacian_data, laplacian_labels


def get_montage_function(montage_type: str):
    """
    Get the appropriate montage function based on type.
    
    Args:
        montage_type: Type of montage ('bipolar', 'average', 'referential')
        
    Returns:
        Montage function
    """
    montage_functions = {
        'bipolar': apply_bipolar_montage,
        'average': apply_average_montage,
        'referential': apply_referential_montage,
        'laplacian': create_laplacian_montage
    }
    
    if montage_type not in montage_functions:
        raise ValueError(f"Unknown montage type: {montage_type}")
    
    return montage_functions[montage_type]