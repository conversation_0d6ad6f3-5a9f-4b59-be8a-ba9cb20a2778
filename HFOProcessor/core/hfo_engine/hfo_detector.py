"""
HFO Detector Module
Core HFO detection algorithms extracted from hfo_analysis.py
"""

import numpy as np
from scipy.signal import find_peaks, hilbert
import logging
from typing import List, Tuple, Dict, Optional, Any
from dataclasses import dataclass

from core.constants import (
    DEFAULT_AMPLITUDE_THRESHOLD_1,
    DEFAULT_AMPLITUDE_THRESHOLD_2,
    DEFAULT_MIN_PEAKS_1,
    DEFAULT_MIN_PEAKS_2,
    DEFAULT_MIN_HFO_DURATION_MS,
    DEFAULT_TEMPORAL_SYNC_MS,
    POWER_THRESHOLD
)

logger = logging.getLogger(__name__)


@dataclass
class HFOEvent:
    """Represents a detected HFO event"""
    channel_idx: int
    start_idx: int
    end_idx: int
    duration_ms: float
    num_peaks: int
    num_peaks_above_threshold: int
    peak_frequency: float = 0.0
    amplitude: float = 0.0
    power: float = 0.0


class HFODetector:
    """Handles HFO detection operations"""

    def __init__(self, sampling_rate: float):
        """
        Initialize HFO detector

        Args:
            sampling_rate: Sampling frequency in Hz
        """
        self.sampling_rate = sampling_rate
        self.samples_per_ms = sampling_rate / 1000

    def detect_hfos(self, signal: np.ndarray,
                   parameters: Dict[str, Any],
                   gui_output: Optional[callable] = None) -> Dict[str, Any]:
        """
        Main HFO detection method

        Args:
            signal: Filtered EEG signal (channels x samples)
            parameters: Detection parameters dictionary
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing detection results
        """
        num_channels, num_samples = signal.shape

        # Extract parameters
        thresh_scheme = 10  # Only scheme 10 is supported
        amplitude_threshold_1 = parameters.get('amplitude_1', DEFAULT_AMPLITUDE_THRESHOLD_1)
        amplitude_threshold_2 = parameters.get('amplitude_2', DEFAULT_AMPLITUDE_THRESHOLD_2)
        min_peaks_1 = parameters.get('peaks_1', DEFAULT_MIN_PEAKS_1)
        min_peaks_2 = parameters.get('peaks_2', DEFAULT_MIN_PEAKS_2)
        min_hfo_duration = parameters.get('duration', DEFAULT_MIN_HFO_DURATION_MS)
        min_separation = parameters.get('temporal_sync', DEFAULT_TEMPORAL_SYNC_MS)
        window_length = parameters.get('window_length', 5)

        # Calculate energy statistics
        if gui_output:
            gui_output('\nCalculating energy statistics...')

        energy_stats = self.calculate_energy_statistics(
            signal, window_length
        )

        # Detect HFO segments
        if gui_output:
            gui_output('Finding HFOs...')

        start_indices, end_indices = self.detect_energy_segments(
            energy_stats['hilbert_envelope'],
            energy_stats['mean_hilbert'],
            energy_stats['std_hilbert'],
            amplitude_threshold_1,
            min_hfo_duration,
            thresh_scheme
        )

        # Merge nearby HFOs
        start_indices, end_indices = self.merge_nearby_hfos(
            start_indices, end_indices, min_separation
        )

        # Validate HFOs based on peak criteria
        validated_hfos = self.validate_hfos_by_peaks(
            signal,
            start_indices,
            end_indices,
            energy_stats['mean_rectified'],
            energy_stats['std_rectified'],
            amplitude_threshold_2,
            min_peaks_1,
            min_peaks_2
        )

        if gui_output:
            gui_output('HFO detection complete.')

        return {
            'start_indices': validated_hfos['start_indices'],
            'end_indices': validated_hfos['end_indices'],
            'rejected_start': validated_hfos['rejected_start'],
            'rejected_end': validated_hfos['rejected_end'],
            'energy_stats': energy_stats,
            'num_channels': num_channels,
            'num_samples': num_samples
        }

    def calculate_energy_statistics(self, signal: np.ndarray,
                                  window_length: int = 5) -> Dict[str, np.ndarray]:
        """
        Calculate energy statistics for HFO detection

        Args:
            signal: Input signal (channels x samples)
            window_length: Window length in ms for RMS calculation

        Returns:
            Dictionary containing energy statistics
        """
        num_channels, num_samples = signal.shape
        window_samples = int(window_length * self.samples_per_ms)

        # Calculate rectified signal statistics
        rectified_signal = np.abs(signal)
        mean_rectified = np.mean(rectified_signal, axis=1)
        std_rectified = np.std(rectified_signal, axis=1)

        # Calculate Hilbert envelope
        # Adjust for window length
        envelope_length = num_samples - (window_samples - 1)
        hilbert_envelope = np.zeros((num_channels, envelope_length))

        for ch in range(num_channels):
            # Use Hilbert transform
            analytic_signal = hilbert(signal[ch, :envelope_length])
            hilbert_envelope[ch, :] = np.abs(analytic_signal)

        # Calculate Hilbert statistics
        mean_hilbert = np.mean(hilbert_envelope, axis=1)
        std_hilbert = np.std(hilbert_envelope, axis=1)

        return {
            'rectified_signal': rectified_signal,
            'mean_rectified': mean_rectified,
            'std_rectified': std_rectified,
            'hilbert_envelope': hilbert_envelope,
            'mean_hilbert': mean_hilbert,
            'std_hilbert': std_hilbert,
            'window_samples': window_samples
        }

    def detect_energy_segments(self, energy_signal: np.ndarray,
                             mean_energy: np.ndarray,
                             std_energy: np.ndarray,
                             threshold_factor: float,
                             min_duration_ms: float,
                             thresh_scheme: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        Detect segments where energy exceeds threshold

        Args:
            energy_signal: Energy signal (channels x samples)
            mean_energy: Mean energy per channel
            std_energy: Standard deviation of energy per channel
            threshold_factor: Threshold multiplier
            min_duration_ms: Minimum HFO duration in ms
            thresh_scheme: Thresholding scheme (only 10 supported)

        Returns:
            Tuple of (start_indices, end_indices) arrays
        """
        if thresh_scheme != 10:
            raise ValueError('Only thresh_scheme 10 is supported')

        num_channels = energy_signal.shape[0]
        min_duration_samples = int(min_duration_ms * self.samples_per_ms)

        # Initialize lists for each channel
        all_starts = []
        all_ends = []

        for ch in range(num_channels):
            # Calculate threshold for this channel
            threshold = mean_energy[ch] + threshold_factor * std_energy[ch]

            channel_starts = []
            channel_ends = []

            # Find segments above threshold
            above_threshold = energy_signal[ch] > threshold
            in_segment = False
            segment_start = 0
            segment_length = 0

            for i in range(len(above_threshold)):
                if above_threshold[i]:
                    if not in_segment:
                        # Start of new segment
                        segment_start = i
                        in_segment = True
                        segment_length = 1
                    else:
                        segment_length += 1
                else:
                    if in_segment:
                        # End of segment
                        if segment_length >= min_duration_samples:
                            channel_starts.append(segment_start)
                            channel_ends.append(i - 1)
                        in_segment = False
                        segment_length = 0

            # Handle segment extending to end
            if in_segment and segment_length >= min_duration_samples:
                channel_starts.append(segment_start)
                channel_ends.append(len(above_threshold) - 1)

            all_starts.append(channel_starts)
            all_ends.append(channel_ends)

        # Convert to numpy arrays with padding
        max_detections = max(len(starts) for starts in all_starts) if all_starts else 0

        if max_detections == 0:
            return np.zeros((num_channels, 1)), np.zeros((num_channels, 1))

        start_array = np.zeros((num_channels, max_detections), dtype=int)
        end_array = np.zeros((num_channels, max_detections), dtype=int)

        for ch in range(num_channels):
            num_detections = len(all_starts[ch])
            if num_detections > 0:
                start_array[ch, :num_detections] = all_starts[ch]
                end_array[ch, :num_detections] = all_ends[ch]

        return start_array, end_array

    def merge_nearby_hfos(self, start_indices: np.ndarray,
                        end_indices: np.ndarray,
                        min_separation_ms: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        Merge HFOs that are close together

        Args:
            start_indices: Start indices array (channels x detections)
            end_indices: End indices array (channels x detections)
            min_separation_ms: Minimum separation between HFOs in ms

        Returns:
            Tuple of merged (start_indices, end_indices)
        """
        min_separation_samples = int(min_separation_ms * self.samples_per_ms)
        num_channels, num_detections = start_indices.shape

        merged_starts = np.copy(start_indices)
        merged_ends = np.copy(end_indices)

        for ch in range(num_channels):
            # Get valid detections for this channel
            valid_mask = start_indices[ch] > 0
            if not np.any(valid_mask):
                continue

            channel_starts = list(start_indices[ch][valid_mask])
            channel_ends = list(end_indices[ch][valid_mask])

            # Merge nearby segments
            i = 0
            while i < len(channel_starts) - 1:
                if channel_starts[i + 1] < channel_ends[i] + min_separation_samples:
                    # Merge with next segment
                    channel_ends[i] = channel_ends[i + 1]
                    del channel_starts[i + 1]
                    del channel_ends[i + 1]
                else:
                    i += 1

            # Update arrays
            merged_starts[ch] = 0
            merged_ends[ch] = 0
            if channel_starts:
                merged_starts[ch, :len(channel_starts)] = channel_starts
                merged_ends[ch, :len(channel_ends)] = channel_ends

        return merged_starts, merged_ends

    def validate_hfos_by_peaks(self, signal: np.ndarray,
                             start_indices: np.ndarray,
                             end_indices: np.ndarray,
                             mean_rectified: np.ndarray,
                             std_rectified: np.ndarray,
                             threshold_factor: float,
                             min_peaks: int,
                             min_peaks_above_threshold: int) -> Dict[str, np.ndarray]:
        """
        Validate HFOs based on peak criteria

        Args:
            signal: Original signal (channels x samples)
            start_indices: Start indices of HFO candidates
            end_indices: End indices of HFO candidates
            mean_rectified: Mean of rectified signal per channel
            std_rectified: Std of rectified signal per channel
            threshold_factor: Threshold multiplier for peak detection
            min_peaks: Minimum total peaks required
            min_peaks_above_threshold: Minimum peaks above threshold

        Returns:
            Dictionary with validated and rejected HFO indices
        """
        num_channels = signal.shape[0]

        # Initialize result arrays
        final_starts = []
        final_ends = []
        rejected_starts = []
        rejected_ends = []
        long_rejected_starts = []
        long_rejected_ends = []

        for ch in range(num_channels):
            channel_final_starts = []
            channel_final_ends = []
            channel_rejected_starts = []
            channel_rejected_ends = []

            # Process each HFO candidate
            for i in range(start_indices.shape[1]):
                if start_indices[ch, i] == 0:
                    break

                start = int(start_indices[ch, i])
                end = int(end_indices[ch, i])

                # Extract HFO segment
                hfo_segment = signal[ch, start:end]
                rectified_segment = np.abs(hfo_segment)

                # Find peaks
                peaks, _ = find_peaks(rectified_segment)

                # Find peaks above threshold
                threshold = mean_rectified[ch] + threshold_factor * std_rectified[ch]
                peaks_above_threshold = peaks[rectified_segment[peaks] > threshold]

                # Validate based on peak criteria
                if len(peaks) >= min_peaks and len(peaks_above_threshold) >= min_peaks_above_threshold:
                    # Valid HFO
                    channel_final_starts.append(start)
                    channel_final_ends.append(end)
                else:
                    # Rejected HFO
                    channel_rejected_starts.append(start)
                    channel_rejected_ends.append(end)

            final_starts.append(channel_final_starts)
            final_ends.append(channel_final_ends)
            rejected_starts.append(channel_rejected_starts)
            rejected_ends.append(channel_rejected_ends)

        # Convert to numpy arrays
        final_starts = self._list_to_padded_array(final_starts)
        final_ends = self._list_to_padded_array(final_ends)
        rejected_starts = self._list_to_padded_array(rejected_starts)
        rejected_ends = self._list_to_padded_array(rejected_ends)

        return {
            'start_indices': final_starts,
            'end_indices': final_ends,
            'rejected_start': rejected_starts,
            'rejected_end': rejected_ends
        }

    def _list_to_padded_array(self, list_of_lists: List[List[int]]) -> np.ndarray:
        """
        Convert list of lists to padded numpy array

        Args:
            list_of_lists: List of lists with varying lengths

        Returns:
            Padded numpy array
        """
        if not list_of_lists:
            return np.zeros((1, 1), dtype=int)

        max_len = max(len(lst) for lst in list_of_lists)
        if max_len == 0:
            return np.zeros((len(list_of_lists), 1), dtype=int)

        result = np.zeros((len(list_of_lists), max_len), dtype=int)
        for i, lst in enumerate(list_of_lists):
            if lst:
                result[i, :len(lst)] = lst

        return result

    def check_hfos_near_blanks(self, start_indices: np.ndarray,
                             end_indices: np.ndarray,
                             blank_starts: List[int],
                             blank_ends: List[int],
                             margin: int = 10) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove HFOs that occur near blank periods

        Args:
            start_indices: HFO start indices
            end_indices: HFO end indices
            blank_starts: Start indices of blank periods
            blank_ends: End indices of blank periods
            margin: Margin around blanks in samples

        Returns:
            Filtered (start_indices, end_indices)
        """
        if not blank_starts:
            return start_indices, end_indices

        num_channels = start_indices.shape[0]
        filtered_starts = np.copy(start_indices)
        filtered_ends = np.copy(end_indices)

        for ch in range(num_channels):
            valid_hfos = []

            for i in range(start_indices.shape[1]):
                if start_indices[ch, i] == 0:
                    break

                hfo_start = start_indices[ch, i]
                hfo_end = end_indices[ch, i]

                # Check if HFO is near any blank
                is_near_blank = False
                for blank_start, blank_end in zip(blank_starts, blank_ends):
                    if (hfo_end >= blank_start - margin and
                        hfo_start <= blank_end + margin):
                        is_near_blank = True
                        break

                if not is_near_blank:
                    valid_hfos.append(i)

            # Update arrays with valid HFOs only
            if valid_hfos:
                filtered_starts[ch] = 0
                filtered_ends[ch] = 0
                for j, idx in enumerate(valid_hfos):
                    filtered_starts[ch, j] = start_indices[ch, idx]
                    filtered_ends[ch, j] = end_indices[ch, idx]
            else:
                filtered_starts[ch] = 0
                filtered_ends[ch] = 0

        return filtered_starts, filtered_ends