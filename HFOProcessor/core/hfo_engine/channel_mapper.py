"""
Channel name mapping utility for standardizing EEG channel labels.
Maps generic channel names (CH1-CH2, etc.) to standard EEG montage names.
"""

from typing import Dict, List, Optional


class ChannelMapper:
    """Maps generic channel names to standard EEG montage names."""

    # Standard 10-20 system bipolar montage mapping
    STANDARD_BIPOLAR_MAPPING = {
        # Generic to standard mapping for common configurations
        "CH1-CH2": "FP1-F7",
        "CH2-CH3": "F7-T3",
        "CH3-CH4": "T3-T5",
        "CH4-CH5": "T5-O1",
        "CH5-CH6": "FP2-F8",
        "CH6-CH7": "F8-T4",
        "CH7-CH8": "T4-T6",
        "CH8-CH9": "T6-O2",
        "CH9-CH10": "FP1-F3",
        "CH10-CH11": "F3-C3",
        "CH11-CH12": "C3-P3",
        "CH12-CH13": "P3-O1",
        "CH13-CH14": "FP2-F4",
        "CH14-CH15": "F4-C4",
        "CH15-CH16": "C4-P4",
        "CH16-CH17": "P4-O2",
        "CH17-CH18": "FZ-CZ",
        "CH18-CH19": "CZ-PZ",
        "CH19-CH20": "EKG1-EKG2",
        "CH20-CH21": "EMG1-EMG2",
    }

    # Alternative mapping for referential montage
    STANDARD_REFERENTIAL_MAPPING = {
        "CH1": "FP1",
        "CH2": "FP2",
        "CH3": "F7",
        "CH4": "F8",
        "CH5": "F3",
        "CH6": "F4",
        "CH7": "T3",
        "CH8": "T4",
        "CH9": "C3",
        "CH10": "C4",
        "CH11": "T5",
        "CH12": "T6",
        "CH13": "P3",
        "CH14": "P4",
        "CH15": "O1",
        "CH16": "O2",
        "CH17": "FZ",
        "CH18": "CZ",
        "CH19": "PZ",
        "CH20": "A1",
        "CH21": "A2",
    }

    def __init__(self, mapping_type: str = "bipolar", custom_mapping: Optional[Dict[str, str]] = None):
        """
        Initialize the channel mapper.

        Args:
            mapping_type: Type of mapping to use ("bipolar", "referential", "custom", or "none")
            custom_mapping: Custom mapping dictionary if mapping_type is "custom"
        """
        self.mapping_type = mapping_type

        if mapping_type == "bipolar":
            self.mapping = self.STANDARD_BIPOLAR_MAPPING.copy()
        elif mapping_type == "referential":
            self.mapping = self.STANDARD_REFERENTIAL_MAPPING.copy()
        elif mapping_type == "custom" and custom_mapping:
            self.mapping = custom_mapping.copy()
        else:
            self.mapping = {}

    def map_channel_name(self, channel_name: str) -> str:
        """
        Map a single channel name.

        Args:
            channel_name: Original channel name

        Returns:
            Mapped channel name or original if no mapping exists
        """
        return self.mapping.get(channel_name, channel_name)

    def map_channel_list(self, channel_list: List[str]) -> List[str]:
        """
        Map a list of channel names.

        Args:
            channel_list: List of original channel names

        Returns:
            List of mapped channel names
        """
        return [self.map_channel_name(ch) for ch in channel_list]

    def map_results(self, results: Dict) -> Dict:
        """
        Map channel names in HFO results dictionary.

        Args:
            results: HFO results dictionary

        Returns:
            Results with mapped channel names
        """
        if not self.mapping:
            return results

        # Map channel_labels
        if "channel_labels" in results:
            results["channel_labels"] = self.map_channel_list(results["channel_labels"])

        # Map channels in metadata
        if "metadata" in results and "channels" in results["metadata"]:
            results["metadata"]["channels"] = self.map_channel_list(results["metadata"]["channels"])

        # Map channel names in HFO events
        if "hfo_events" in results:
            for event in results["hfo_events"]:
                if "channel" in event:
                    event["channel"] = self.map_channel_name(event["channel"])

        # Map channel data keys
        if "channel_data" in results:
            new_channel_data = {}
            for old_name, data in results["channel_data"].items():
                new_name = self.map_channel_name(old_name)
                new_channel_data[new_name] = data
            results["channel_data"] = new_channel_data

        # Map statistics
        if "statistics" in results:
            stats = results["statistics"]

            # Map channels_with_hfos
            if "channels_with_hfos" in stats:
                stats["channels_with_hfos"] = self.map_channel_list(stats["channels_with_hfos"])

            # Map hfo_rate_per_channel keys
            if "hfo_rate_per_channel" in stats:
                new_rates = {}
                for old_name, rate in stats["hfo_rate_per_channel"].items():
                    new_name = self.map_channel_name(old_name)
                    new_rates[new_name] = rate
                stats["hfo_rate_per_channel"] = new_rates

        return results

    @staticmethod
    def detect_channel_format(channel_list: List[str]) -> str:
        """
        Detect the format of channel names.

        Args:
            channel_list: List of channel names

        Returns:
            Detected format: "generic", "standard", or "mixed"
        """
        generic_count = sum(1 for ch in channel_list if ch.startswith("CH"))
        standard_count = sum(1 for ch in channel_list if any(
            std in ch for std in ["FP", "F", "T", "C", "P", "O", "Z"]
        ))

        if generic_count > len(channel_list) * 0.8:
            return "generic"
        elif standard_count > len(channel_list) * 0.8:
            return "standard"
        else:
            return "mixed"