"""
Result formatting module for HFO detection.
Handles formatting and organization of HFO detection results.
"""

import numpy as np
from typing import Dict, List, Any, Optional


class HFOResultFormatter:
    """Formats HFO detection results for output"""

    @staticmethod
    def format_results(
        win_len2, std<PERSON><PERSON><PERSON>, mean<PERSON><PERSON><PERSON>, RMS2,
        rejected_start_ind, rejected_end_ind,
        rejecLONG_start_ind, rejecLONG_end_ind,
        final_start_ind, final_end_ind,
        lfo_start_ind, lfo_end_ind,
        noise_start_ind, noise_end_ind,
        myEEG, channel_labels, end_channel,
        con_fact, con_factN,
        datalen_resulted_sec, num_pts, samp_freq, num_min,
        counter, duration, peak_freq, my_amp, hfo_power, max_freq,
        avg_peak_freq, avg_my_amp, avg_hfo_power, avg_max_freq, avg_duration,
        input_file_path
    ) -> Dict[str, Any]:
        """
        Format all HFO detection results into a structured dictionary.

        This function was extracted from the main algorithm to improve organization
        while preserving the exact data structure and format.
        """
        result = {
            "win_len2": win_len2,
            "stdHilbert": stdHilbert,
            "meanHilbert": meanHilbert,
            "RMS2": RMS2,
            "rejected_start_ind": rejected_start_ind,
            "rejected_end_ind": rejected_end_ind,
            "rejecLONG_start_ind": rejecLONG_start_ind,
            "rejecLONG_end_ind": rejecLONG_end_ind,
            "final_start_ind": final_start_ind,
            "final_end_ind": final_end_ind,
            "lfo_start_ind": lfo_start_ind,
            "lfo_end_ind": lfo_end_ind,
            "noise_start_ind": noise_start_ind,
            "noise_end_ind": noise_end_ind,
            "myEEG": myEEG,
            "channel_labels": channel_labels,
            "end_channel": end_channel,
            "con_fact": con_fact,
            "con_factN": con_factN,
            "datalen_resulted_sec": datalen_resulted_sec,
            "num_pts": num_pts,
            "samp_freq": samp_freq,
            "num_min": num_min,
            "counter": counter,
            "duration": duration,
            "peak_freq": peak_freq,
            "my_amp": my_amp,
            "hfo_power": hfo_power,
            "max_freq": max_freq,
            "avg_peak_freq": avg_peak_freq,
            "avg_my_amp": avg_my_amp,
            "avg_hfo_power": avg_hfo_power,
            "avg_max_freq": avg_max_freq,
            "avg_duration": avg_duration,
            "input_file_path": input_file_path,
            "success": True
        }

        return result

    @staticmethod
    def calculate_average_characteristics(
        duration: List[List],
        hfo_power: List[List],
        peak_freq: List[List],
        my_amp: List[List],
        max_freq: List[List],
        num_channels: int,
        start_channel: int,
        end_channel: int
    ) -> tuple:
        """
        Calculate average HFO characteristics per channel.

        Returns:
            Tuple of (avg_duration, avg_hfo_power, avg_peak_freq, avg_my_amp, avg_max_freq)
        """
        # Initialize average values
        avg_duration = np.zeros(num_channels)
        avg_hfo_power = np.zeros(num_channels)
        avg_peak_freq = np.zeros(num_channels)
        avg_my_amp = np.zeros(num_channels)
        avg_max_freq = np.zeros(num_channels)

        # Calculate averages for each channel
        for j in range(start_channel - 1, end_channel):
            if duration[j]:
                avg_duration[j] = np.mean(duration[j])
            if hfo_power[j]:
                avg_hfo_power[j] = np.mean(
                    np.log10(hfo_power[j]))  # Log10 for power
            if peak_freq[j]:
                avg_peak_freq[j] = np.mean(peak_freq[j])
            if my_amp[j]:
                avg_my_amp[j] = np.mean(my_amp[j])
            if max_freq[j]:
                avg_max_freq[j] = np.mean(max_freq[j])

        return avg_duration, avg_hfo_power, avg_peak_freq, avg_my_amp, avg_max_freq

    @staticmethod
    def convert_indices_to_arrays(
        final_start_ind: List[List],
        final_end_ind: List[List],
        lfo_start_ind: List[List],
        lfo_end_ind: List[List],
        noise_start_ind: List[List],
        noise_end_ind: List[List]
    ) -> tuple:
        """
        Convert list indices to numpy arrays for consistency.

        Returns:
            Tuple of converted arrays
        """
        final_start_ind = [np.array(lst) for lst in final_start_ind]
        final_end_ind = [np.array(lst) for lst in final_end_ind]
        lfo_start_ind = [np.array(lst) for lst in lfo_start_ind]
        lfo_end_ind = [np.array(lst) for lst in lfo_end_ind]
        noise_start_ind = [np.array(lst) for lst in noise_start_ind]
        noise_end_ind = [np.array(lst) for lst in noise_end_ind]

        return (final_start_ind, final_end_ind, lfo_start_ind,
                lfo_end_ind, noise_start_ind, noise_end_ind)


def convert_numpy_types(obj):
    """
    Recursively convert numpy types to Python native types
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                        np.uint8, np.uint16, np.uint32, np.uint64)):
        return int(obj)
    elif isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
        return float(obj)
    elif isinstance(obj, (np.complex_, np.complex64, np.complex128)):
        return {'real': float(obj.real), 'imag': float(obj.imag)}
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    else:
        return obj


def format_results_for_api(hfo_results: Dict[str, Any], eeg_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format raw HFO analysis results for API response.

    Args:
        hfo_results: Raw results from HFO detection algorithm
        eeg_data: Original EEG data structure

    Returns:
        Formatted results dictionary for API
    """
    # Extract HFO events from results
    hfo_events = extract_hfo_events(hfo_results)

    # Get channel data (filtered signals) - limit for API response
    channel_data_result = extract_channel_data(hfo_results, eeg_data)
    channel_data = channel_data_result["channel_data"]
    sampling_info = channel_data_result["sampling_info"]

    # Calculate statistics
    statistics = calculate_statistics(hfo_results, hfo_events, eeg_data)

    # Build metadata
    metadata = {
        "filename": hfo_results.get("input_file_path", "unknown.edf"),
        "sampling_rate": hfo_results.get("samp_freq", eeg_data.get("srate", 256)),
        "duration_seconds": hfo_results.get("segment_length",
                                            hfo_results.get("datalen_resulted_sec",
                                                            eeg_data['data'].shape[1] / eeg_data.get("srate", 256))),
        "channels": hfo_results.get("channel_labels", eeg_data.get("chanlocs", [])),
        "processing_time": 0,  # Will be updated by processor
    }

    # Clean up raw results - exclude large arrays that aren't needed for API
    cleaned_results = {}
    exclude_keys = {'myEEG', 'RMS2', 'myEEGcfa'}  # Large arrays not needed in API response

    for key, value in hfo_results.items():
        if key not in exclude_keys:
            cleaned_results[key] = convert_numpy_types(value)

    # Return formatted results with cleaned raw data for report generation
    return {
        "metadata": convert_numpy_types(metadata),
        "statistics": convert_numpy_types(statistics),
        "channel_data": convert_numpy_types(channel_data),
        "sampling_info": convert_numpy_types(sampling_info),
        "hfo_events": convert_numpy_types(hfo_events),
        # Store cleaned raw results for comprehensive report generation
        **cleaned_results
    }


def extract_hfo_events(results: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Extract HFO events from raw results, including both accepted and rejected events.
    """
    hfo_events = []

    # Get HFO indices and channel info
    channel_labels = results.get("channel_labels", [])
    samp_freq = results.get("samp_freq", 256)

    # Get HFO characteristics (may be nested lists or empty)
    duration = results.get("duration", [])
    peak_freq = results.get("peak_freq", [])
    my_amp = results.get("my_amp", [])
    hfo_power = results.get("hfo_power", [])

    # Extract accepted HFOs
    final_start_ind = results.get("final_start_ind", [])
    final_end_ind = results.get("final_end_ind", [])

    # Extract rejected HFOs (too few peaks)
    rejected_start_ind = results.get("rejected_start_ind", [])
    rejected_end_ind = results.get("rejected_end_ind", [])

    # Extract long rejected HFOs
    rejecLONG_start_ind = results.get("rejecLONG_start_ind", [])
    rejecLONG_end_ind = results.get("rejecLONG_end_ind", [])

    # Extract low frequency oscillations (rejected)
    lfo_start_ind = results.get("lfo_start_ind", [])
    lfo_end_ind = results.get("lfo_end_ind", [])

    # Extract noise-related rejected HFOs
    noise_start_ind = results.get("noise_start_ind", [])
    noise_end_ind = results.get("noise_end_ind", [])

    # Helper function to process HFO events
    def process_hfo_indices(start_indices, end_indices, event_type):
        for ch_idx, ch_label in enumerate(channel_labels):
            if ch_idx < len(start_indices) and ch_idx < len(end_indices):
                ch_starts = start_indices[ch_idx]
                ch_ends = end_indices[ch_idx]

                # Handle both list and numpy array formats
                if hasattr(ch_starts, '__iter__'):
                    # Convert to list if numpy array
                    if hasattr(ch_starts, 'tolist'):
                        ch_starts = ch_starts.tolist()
                        ch_ends = ch_ends.tolist()

                    # Iterate through HFOs in this channel
                    for i, (start, end) in enumerate(zip(ch_starts, ch_ends)):
                        if start > 0:  # Valid HFO (0 indicates no HFO)
                            # Get characteristics if available (primarily for accepted HFOs)
                            event_peak_freq = 0
                            event_amplitude = 0
                            event_power = 0

                            if event_type == 'accepted':
                                if ch_idx < len(peak_freq) and peak_freq[ch_idx] and i < len(peak_freq[ch_idx]):
                                    event_peak_freq = float(peak_freq[ch_idx][i])

                                if ch_idx < len(my_amp) and my_amp[ch_idx] and i < len(my_amp[ch_idx]):
                                    event_amplitude = float(my_amp[ch_idx][i])

                                if ch_idx < len(hfo_power) and hfo_power[ch_idx] and i < len(hfo_power[ch_idx]):
                                    event_power = float(hfo_power[ch_idx][i])

                            hfo_event = {
                                "channel": ch_label,
                                "type": event_type,
                                "start_time": round(float(start / samp_freq), 3),
                                "end_time": round(float(end / samp_freq), 3),
                                "peak_frequency": round(event_peak_freq, 1),
                                "amplitude": round(event_amplitude, 2),
                                "duration_ms": round(float((end - start) * 1000 / samp_freq), 1),
                                "power": round(event_power, 2),
                            }
                            hfo_events.append(hfo_event)

    # Process all types of HFO events
    process_hfo_indices(final_start_ind, final_end_ind, 'accepted')
    process_hfo_indices(rejected_start_ind, rejected_end_ind, 'rejected')
    process_hfo_indices(rejecLONG_start_ind, rejecLONG_end_ind, 'rejected_long')
    process_hfo_indices(lfo_start_ind, lfo_end_ind, 'lfo_rejected')
    process_hfo_indices(noise_start_ind, noise_end_ind, 'noise_rejected')

    # Sort events by start time
    hfo_events.sort(key=lambda x: x["start_time"])
    return hfo_events


def extract_channel_data(results: Dict[str, Any], eeg_data: Dict[str, Any]) -> Dict[str, List[float]]:
    """
    Extract filtered channel data for visualization.
    Includes ALL channels from the EDF file with intelligent downsampling.
    """
    import logging
    logger = logging.getLogger(__name__)

    channel_data = {}
    sampling_info = {}

    # Use filtered EEG data if available
    myEEG = results.get("myEEG")
    if myEEG is None:
        myEEG = results.get("myEEGcfa", eeg_data.get("data", []))

    channel_labels = results.get(
        "channel_labels", eeg_data.get("chanlocs", []))

    # Get original sampling rate and duration
    original_sampling_rate = results.get("samp_freq", eeg_data.get("sampling_rate", 256))
    duration_seconds = results.get("datalen_resulted_sec", 60)

    # Log the actual number of channels being processed
    logger.info(f"Processing {len(channel_labels)} channels for visualization")

    # Create channel data dictionary with intelligent downsampling
    # Process ALL channels without artificial limits
    for i, ch_label in enumerate(channel_labels):
        if i < len(myEEG):
            signal = myEEG[i]

            # Convert to list if numpy array
            if hasattr(signal, 'tolist'):
                signal = signal.tolist()

            # Intelligent downsampling based on signal length
            # For very long signals, we need more aggressive downsampling
            max_points = 10000  # Maximum points per channel for reasonable performance
            original_length = len(signal)

            if len(signal) > max_points:
                # Use decimation to preserve signal characteristics
                step = len(signal) // max_points
                # Take every 'step' sample to reduce data size
                signal = signal[::step][:max_points]

                # Calculate effective sampling rate after downsampling
                effective_sampling_rate = len(signal) / duration_seconds
                sampling_info["downsampled"] = True
                sampling_info["downsample_factor"] = step
                sampling_info["effective_sampling_rate"] = effective_sampling_rate
            else:
                effective_sampling_rate = original_sampling_rate
                sampling_info["downsampled"] = False
                sampling_info["effective_sampling_rate"] = effective_sampling_rate

            # Store the processed signal for this channel
            channel_data[ch_label] = signal

    logger.info(
        f"Successfully extracted data for {len(channel_data)} channels")

    # Add sampling info to the response
    sampling_info["original_sampling_rate"] = original_sampling_rate
    sampling_info["duration_seconds"] = duration_seconds

    return {"channel_data": channel_data, "sampling_info": sampling_info}


def calculate_statistics(results: Dict[str, Any], hfo_events: List[Dict], eeg_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate HFO statistics from results.
    """
    # Get basic counts
    total_hfos = len(hfo_events)

    # Get duration in seconds
    duration_seconds = results.get("datalen_resulted_sec")
    if not duration_seconds:
        duration_seconds = results.get("segment_length")
    if not duration_seconds and eeg_data.get('data') is not None:
        duration_seconds = eeg_data['data'].shape[1] / \
            eeg_data.get("srate", 256)
    if not duration_seconds:
        duration_seconds = 1  # Default to avoid division by zero

    # Calculate HFO density (HFOs per minute)
    hfo_density = (total_hfos / duration_seconds *
                   60) if duration_seconds > 0 else 0

    # Get channels with HFOs
    channels_with_hfos = list(set(event["channel"] for event in hfo_events))

    # Calculate HFO rate per channel
    hfo_rate_per_channel = {}
    channel_labels = results.get(
        "channel_labels", eeg_data.get("chanlocs", []))

    for channel in channel_labels:
        channel_hfos = [e for e in hfo_events if e["channel"] == channel]
        rate = (len(channel_hfos) / duration_seconds *
                60) if duration_seconds > 0 else 0
        hfo_rate_per_channel[channel] = round(rate, 2)

    # Get additional stats from results
    # Robust extraction of optional fields that may be numpy arrays
    rejected_val = results.get("rejected", 0)
    try:
        rejected_hfos = int(rejected_val)
    except Exception:
        rejected_hfos = 0

    con_fact_val = results.get("con_fact", None)
    if con_fact_val is None:
        connectivity_factor = 0.0
    else:
        try:
            # If array-like, use mean across channels
            connectivity_factor = float(np.mean(con_fact_val))
        except Exception:
            # Fallback to best-effort float conversion
            try:
                connectivity_factor = float(con_fact_val)
            except Exception:
                connectivity_factor = 0.0

    statistics = {
        "total_hfos": total_hfos,
        "hfo_density": round(hfo_density, 2),
        "channels_with_hfos": channels_with_hfos,
        "hfo_rate_per_channel": hfo_rate_per_channel,
        "duration_minutes": round(duration_seconds / 60, 2),
        "rejected_hfos": rejected_hfos,
        "connectivity_factor": round(connectivity_factor, 2),
    }

    return statistics
