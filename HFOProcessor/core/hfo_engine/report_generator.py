"""
HFO Analysis Report Generator
Generates comprehensive CSV reports matching the original desktop application format
"""

import os
import re
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


def extract_custom_sort_key(label: str, group_order: Dict[str, int]) -> tuple:
    """
    Extract a sort key from a channel label for proper ordering.

    Args:
        label: Channel label (e.g., "A11-12")
        group_order: Dictionary mapping prefixes to order values

    Returns:
        Tuple of (order, number) for sorting
    """
    first_part = label.split('-')[0]
    m = re.match(r"([A-Za-z]+)(\d+)", first_part)
    if m:
        prefix = m.group(1)
        number = int(m.group(2))
        order = group_order.get(prefix, 1e9)
        return (order, number)
    return (1e9, 0)


def reorder_channels(channel_labels: List[str], channel_data: Dict[str, Any]) -> tuple:
    """
    Reorder channels based on custom sorting logic.

    Args:
        channel_labels: List of channel labels
        channel_data: Dictionary containing channel-specific data

    Returns:
        Tuple of (sorted_labels, sorted_indices)
    """
    # Build group order dictionary
    group_order = {}
    order = 0
    for label in channel_labels:
        first_part = label.split('-')[0]
        m = re.match(r"([A-Za-z]+)", first_part)
        if m:
            prefix = m.group(1)
            if prefix not in group_order:
                group_order[prefix] = order
                order += 1

    # Get sorted indices
    indexed_labels = [(label, i) for i, label in enumerate(channel_labels)]
    sorted_data = sorted(indexed_labels, key=lambda x: extract_custom_sort_key(x[0], group_order))

    sorted_labels = [label for label, _ in sorted_data]
    sorted_indices = [idx for _, idx in sorted_data]

    return sorted_labels, sorted_indices


def parse_patient_info(header: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse patient information from EDF header.

    Args:
        header: EDF file header dictionary

    Returns:
        Dictionary containing patient information
    """
    patient_id = header.get('patientID', '')

    # Check if patient ID is anonymized
    if patient_id == "X X X X" or not re.search(r'\w+', patient_id):
        return {
            'patient_code': 'Anonymous',
            'gender': 'Unknown',
            'birth_date': 'Unknown',
            'patient_name': 'Anonymous',
            'age': 'Unknown'
        }

    # Try to parse patient data
    match = re.match(
        r'(?P<patient_code>\w+) (?P<gender>[FMfm]) (?P<birth_date>\d\d-\w\w\w-\d\d\d\d) (?P<patient_name>\w+)',
        patient_id
    )

    if match:
        patient_data = match.groupdict()
    else:
        # Fallback for non-standard format
        patient_data = {
            'patient_code': patient_id,
            'gender': 'Unknown',
            'birth_date': 'Unknown',
            'patient_name': patient_id
        }

    # Calculate age if birth date is available
    age = 'Unknown'
    if patient_data['birth_date'] not in ['Unknown', None, '']:
        try:
            birth_date = datetime.strptime(patient_data['birth_date'], '%d-%b-%Y')
            if birth_date <= datetime.now():
                from dateutil.relativedelta import relativedelta
                age = relativedelta(datetime.now(), birth_date).years
        except:
            pass

    patient_data['age'] = age
    return patient_data


def generate_csv_report(
    parameters: Dict[str, Any],
    header: Dict[str, Any],
    file_name: str,
    locutoff: float = 80,
    hicutoff: float = 500,
    output_dir: Optional[str] = None
) -> str:
    """
    Generate a comprehensive CSV report for HFO analysis results.

    Args:
        parameters: Dictionary containing all HFO analysis results
        header: EDF file header information
        file_name: Name of the analyzed file
        locutoff: Low frequency cutoff
        hicutoff: High frequency cutoff
        output_dir: Directory to save the report (optional)

    Returns:
        String containing the CSV report content
    """
    # Extract parameters
    channel_labels = parameters.get('channel_labels', [])
    counter = parameters.get('counter', [])
    density = parameters.get('density', [])
    con_factN = parameters.get('con_factN', [])
    avg_duration = parameters.get('avg_duration', [])
    avg_peak_freq = parameters.get('avg_peak_freq', [])
    avg_hfo_power = parameters.get('avg_hfo_power', [])
    avg_my_amp = parameters.get('avg_my_amp', [])
    avg_max_freq = parameters.get('avg_max_freq', [])

    num_channels = parameters.get('num_channels', 0)
    start_channel = parameters.get('start_channel', 1)
    end_channel = parameters.get('end_channel', num_channels)
    montage = parameters.get('montage', 'Unknown')
    use_as_ref = parameters.get('use_as_ref', 'Unknown')
    analysis_start = parameters.get('analysis_start', 0)
    analysis_end = parameters.get('analysis_end', 0)
    segment_length = parameters.get('segment_length', 0)

    samp_freq = parameters.get('samp_freq', 0)
    num_pts = parameters.get('num_pts', 0)
    num_min = parameters.get('num_min', 0)
    rejected = parameters.get('rejected', 0)

    # Reorder channels for consistent output
    if channel_labels:
        sorted_labels, sorted_indices = reorder_channels(
            channel_labels[start_channel-1:end_channel],
            parameters
        )

        # Reorder all channel-specific data
        def reorder_array(arr):
            if isinstance(arr, (list, np.ndarray)) and len(arr) > 0:
                subset = [arr[i + start_channel - 1] for i in sorted_indices]
                return subset
            return arr

        channel_labels = sorted_labels
        counter = reorder_array(counter)
        density = reorder_array(density)
        con_factN = reorder_array(con_factN)
        avg_duration = reorder_array(avg_duration)
        avg_peak_freq = reorder_array(avg_peak_freq)
        avg_hfo_power = reorder_array(avg_hfo_power)
        avg_my_amp = reorder_array(avg_my_amp)
        avg_max_freq = reorder_array(avg_max_freq)

    # Parse patient information
    patient_info = parse_patient_info(header)

    # Calculate timing information
    starttime = datetime.strptime(
        header.get('startdate', '01.01.00') + " " + header.get('starttime', '00.00.00'),
        "%d.%m.%y %H.%M.%S"
    )
    segment_start = starttime + timedelta(seconds=analysis_start)
    segment_end = starttime + timedelta(seconds=analysis_end)

    # Get number of implanted contacts
    implanted_contacts = np.sum([
        label != 'EDF Annotations' and label != 'EDFANNOTATIONS'
        for label in header.get('label', [])
    ])

    # Build CSV report content
    report_lines = []

    # Demographics section
    report_lines.append('Demographics')
    report_lines.append(
        f"Patient name: {patient_info['patient_name']} | "
        f"MRN Hospital: {patient_info['patient_code']}"
    )
    report_lines.append(
        f"DOB: {patient_info['birth_date']} | "
        f"Age (Years): {patient_info['age']} | "
        f"Gender: {patient_info['gender']}"
    )
    report_lines.append('')

    # Recording parameters section
    report_lines.append('Recording parameters')
    report_lines.append(
        f"Sampling rate (Hz): {samp_freq} | "
        f"Num of implanted contacts: {implanted_contacts}"
    )
    report_lines.append('')

    # Analyzed segment section
    report_lines.append('Analyzed segment')
    report_lines.append(f"File name: {file_name}")
    report_lines.append(
        f"Date: {segment_start:%Y.%m.%d} | "
        f"Start time: {segment_start:%H:%M:%S} | "
        f"End time: {segment_end:%H:%M:%S} | "
        f"Length (sec): {segment_length:.2f}"
    )
    report_lines.append(
        f"Montage: {montage} | Reference: {use_as_ref} | Patient state: "
    )
    report_lines.append(
        f"Num. channels analyzed: {num_channels} | "
        f"Frequency band: {locutoff}-{hicutoff} Hz"
    )

    total_hfos = np.sum(counter) if counter else 0
    report_lines.append(
        f"Total num. of HFOs: {total_hfos} | "
        f"Total num. of LFOs (rejected HFOs): {rejected} |"
    )
    report_lines.append('')
    report_lines.append('')

    # HFO profiles section
    report_lines.append('----------------------------HFO profiles: numerical---------------------------')

    # Header with fixed-width columns
    header_line = "| {:<5} | {:<15} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} | {:<8} |".format(
        "Ch#", "ChName", "Rate", "Dens", "Conn", "Durn", "PkFq", "LogPwr", "AvgAmp", "MaxFq"
    )
    separator = "-" * len(header_line)

    report_lines.append(header_line)
    report_lines.append(separator)

    # Data rows for each channel
    for i, ch_name in enumerate(channel_labels):
        # Safe access to array elements
        rate = counter[i] / num_min if num_min > 0 and i < len(counter) else 0
        dens = density[i] / num_min if num_min > 0 and i < len(density) else 0
        conn = con_factN[i] if i < len(con_factN) else 0
        durn = avg_duration[i] if i < len(avg_duration) else 0
        pkfq = avg_peak_freq[i] if i < len(avg_peak_freq) else 0
        logpwr = avg_hfo_power[i] if i < len(avg_hfo_power) else 0
        avgamp = avg_my_amp[i] if i < len(avg_my_amp) else 0
        maxfq = avg_max_freq[i] if i < len(avg_max_freq) else 0

        line = "| {:<5} | {:<15} | {:<8.0f} | {:<8.0f} | {:<8.1f} | {:<8.0f} | {:<8.0f} | {:<8.1f} | {:<8.0f} | {:<8.0f} |".format(
            i + 1,         # Channel number
            ch_name,       # Channel name
            rate,          # Rate
            dens,          # Density
            conn,          # Connectivity
            durn,          # Duration
            pkfq,          # Peak frequency
            logpwr,        # Log power
            avgamp,        # Average amplitude
            maxfq          # Max frequency
        )
        report_lines.append(line)

    # Join all lines into a single string
    csv_content = '\n'.join(report_lines)

    # Save to file if output directory is specified
    if output_dir:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"report_{file_name}_{current_time}.csv"
        file_path = os.path.join(output_dir, report_filename)

        with open(file_path, 'w') as f:
            f.write(csv_content)

        logger.info(f"CSV report saved to: {file_path}")

    return csv_content


def generate_hfo_events_csv(hfo_events: List[Dict[str, Any]]) -> str:
    """
    Generate a simple CSV of HFO events for export.

    Args:
        hfo_events: List of HFO event dictionaries

    Returns:
        CSV string of HFO events
    """
    if not hfo_events:
        return "No HFO events detected"

    # CSV header
    lines = ["Channel,Start Time (s),End Time (s),Duration (ms),Peak Frequency (Hz),Amplitude (μV)"]

    # Add each event
    for event in hfo_events:
        channel = event.get('channel', '')
        start_time = event.get('start_time', 0)
        end_time = event.get('end_time', 0)
        duration = (end_time - start_time) * 1000  # Convert to ms
        peak_freq = event.get('peak_frequency', 0)
        amplitude = event.get('amplitude', 0)

        lines.append(f"{channel},{start_time:.3f},{end_time:.3f},{duration:.1f},{peak_freq:.1f},{amplitude:.2f}")

    return '\n'.join(lines)