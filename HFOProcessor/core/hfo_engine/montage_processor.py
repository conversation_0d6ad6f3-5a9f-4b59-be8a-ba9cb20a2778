"""
Montage Processor
Handles EEG montage transformations (Bipolar, Average, Referential)
"""

import re
import numpy as np
import logging
from typing import List, Tuple, Optional, Dict

logger = logging.getLogger(__name__)


class MontageProcessor:
    """Processes different EEG montage configurations"""

    def __init__(self):
        """Initialize montage processor"""
        self.montage_types = {
            'Bipolar': self.apply_bipolar_montage,
            'Average': self.apply_average_montage,
            'Referential': self.apply_referential_montage
        }

    def apply_montage(self, eeg_data: np.ndarray,
                     channel_labels: List[str],
                     montage_type: str,
                     reference_channel: Optional[str] = None,
                     gui_output: Optional[callable] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Apply the specified montage to EEG data

        Args:
            eeg_data: EEG data matrix (channels x samples)
            channel_labels: Original channel labels
            montage_type: Type of montage ('Bipolar', 'Average', 'Referential')
            reference_channel: Reference channel for referential montage
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (montage_data, montage_labels)
        """
        # Validate montage type
        if montage_type not in self.montage_types:
            error_msg = f'ERROR: Unknown montage type: {montage_type}'
            if gui_output:
                gui_output(error_msg)
            else:
                logger.error(error_msg)
            raise ValueError(error_msg)

        # Apply the appropriate montage
        montage_func = self.montage_types[montage_type]
        montage_data, montage_labels = montage_func(
            eeg_data, channel_labels, reference_channel, gui_output
        )

        # EEG convention: multiply by -1 (positive needs to be displayed as negative)
        montage_data = -1 * montage_data

        return montage_data, montage_labels

    def apply_bipolar_montage(self, eeg_data: np.ndarray,
                             channel_labels: List[str],
                             reference_channel: Optional[str] = None,
                             gui_output: Optional[callable] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Apply bipolar montage

        Args:
            eeg_data: EEG data matrix
            channel_labels: Original channel labels
            reference_channel: Not used for bipolar montage
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (bipolar_data, bipolar_labels)
        """
        montage_data = []
        montage_labels = []

        for i, channel1 in enumerate(channel_labels):
            if str(channel1) != 'REF':
                channel1 = str(channel1)

                # Generate channel2 by incrementing the channel number
                channel2 = self._increment_channel_number(channel1)

                # Find indices for both channels
                index1 = self._find_channel_index(channel1, channel_labels)
                index2 = self._find_channel_index(channel2, channel_labels)

                # Create bipolar pair if both channels exist
                if index1 is not None and index2 is not None:
                    # Subtract channel2 from channel1
                    bipolar_signal = eeg_data[index1, :] - eeg_data[index2, :]
                    montage_data.append(bipolar_signal)
                    montage_labels.append(f'{channel1}-{channel2}')

                    if gui_output:
                        gui_output(f'Created bipolar pair: {channel1}-{channel2}')

        return np.array(montage_data), montage_labels

    def apply_average_montage(self, eeg_data: np.ndarray,
                             channel_labels: List[str],
                             reference_channel: Optional[str] = None,
                             gui_output: Optional[callable] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Apply average reference montage

        Args:
            eeg_data: EEG data matrix
            channel_labels: Original channel labels
            reference_channel: Not used for average montage
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (average_ref_data, average_ref_labels)
        """
        # Calculate average of all channels
        average_reference = np.mean(eeg_data, axis=0)

        montage_data = []
        montage_labels = []

        # Subtract average from each channel
        for i, channel in enumerate(channel_labels):
            referenced_signal = eeg_data[i, :] - average_reference
            montage_data.append(referenced_signal)
            montage_labels.append(f'{channel}-AVG')

        return np.array(montage_data), montage_labels

    def apply_referential_montage(self, eeg_data: np.ndarray,
                                 channel_labels: List[str],
                                 reference_channel: Optional[str] = None,
                                 gui_output: Optional[callable] = None) -> Tuple[np.ndarray, List[str]]:
        """
        Apply referential montage

        Args:
            eeg_data: EEG data matrix
            channel_labels: Original channel labels
            reference_channel: Reference channel name
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (referential_data, referential_labels, used_reference)
        """
        # Determine reference channel
        if reference_channel:
            ref_index = self._find_channel_index(reference_channel, channel_labels)
            if ref_index is None:
                error_msg = f'Reference channel {reference_channel} not found'
                if gui_output:
                    gui_output(error_msg)
                raise ValueError(error_msg)
            used_reference = reference_channel
        else:
            # Find most stable channel as reference
            ref_index, used_reference = self._find_most_stable_channel(
                eeg_data, channel_labels, gui_output
            )

        # Get reference signal
        reference_signal = eeg_data[ref_index, :]

        montage_data = []
        montage_labels = []

        # Subtract reference from each channel
        for i, channel in enumerate(channel_labels):
            referenced_signal = eeg_data[i, :] - reference_signal
            montage_data.append(referenced_signal)
            montage_labels.append(f'{channel}-{used_reference}')

        return np.array(montage_data), montage_labels

    def _increment_channel_number(self, channel_name: str) -> str:
        """
        Increment the number in a channel name

        Args:
            channel_name: Original channel name (e.g., 'CH1')

        Returns:
            Channel name with incremented number (e.g., 'CH2')
        """
        return re.sub(r'(\d+)', lambda x: str(int(x.group()) + 1), channel_name)

    def _find_channel_index(self, channel_name: str,
                           channel_labels: List[str]) -> Optional[int]:
        """
        Find the index of a channel in the labels list

        Args:
            channel_name: Channel to find
            channel_labels: List of channel labels

        Returns:
            Index of the channel or None if not found
        """
        try:
            indices = np.where(np.array(channel_labels) == channel_name)[0]
            return indices[0] if len(indices) > 0 else None
        except Exception:
            return None

    def _find_most_stable_channel(self, eeg_data: np.ndarray,
                                 channel_labels: List[str],
                                 gui_output: Optional[callable] = None) -> Tuple[int, str]:
        """
        Find the most stable channel to use as reference

        Args:
            eeg_data: EEG data matrix
            channel_labels: Channel labels
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (channel_index, channel_name)
        """
        min_variance = float('inf')
        best_channel_idx = 0

        for i, channel in enumerate(channel_labels):
            channel_signal = eeg_data[i, :]

            # Calculate stability metrics
            signal_mean = np.mean(channel_signal)
            signal_sorted = np.sort(channel_signal)
            p25 = int(0.25 * len(signal_sorted))
            p75 = int(0.75 * len(signal_sorted))
            iqr = signal_sorted[p75] - signal_sorted[p25]

            if gui_output:
                gui_output(
                    f'Channel: {channel}, mean={signal_mean:.2f}, '
                    f'p25={signal_sorted[p25]:.2f}, p75={signal_sorted[p75]:.2f}, '
                    f'IQR={iqr:.2f}'
                )

            # Find channel with minimum variance
            if abs(signal_mean) < abs(min_variance):
                min_variance = signal_mean
                best_channel_idx = i

        best_channel_name = channel_labels[best_channel_idx]
        if gui_output:
            gui_output(f'Selected {best_channel_name} as reference channel')

        return best_channel_idx, best_channel_name

    def get_channel_count_after_montage(self, num_channels: int,
                                       montage_type: str) -> int:
        """
        Calculate the number of channels after montage transformation

        Args:
            num_channels: Original number of channels
            montage_type: Type of montage

        Returns:
            Expected number of channels after montage
        """
        if montage_type == 'Bipolar':
            # Bipolar reduces channel count by 1 (pairs)
            return max(num_channels - 1, 0)
        else:
            # Average and Referential maintain channel count
            return num_channels