from typing import Any, Dict, List, Optional, Tuple
from abc import ABC, abstractmethod
import logging

logger = logging.getLogger(__name__)

class BaseValidator(ABC):
    """Base class for all validators in the Biormika system"""
    
    def __init__(self):
        self.errors: List[str] = []
        self.warnings: List[str] = []
        self.info: List[str] = []
        
    @abstractmethod
    def validate(self, data: Any) -> Tuple[bool, List[str]]:
        """
        Validate the given data.
        
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        pass
    
    def add_error(self, message: str):
        """Add an error message to the validation result"""
        self.errors.append(message)
        logger.error(f"Validation error: {message}")
        
    def add_warning(self, message: str):
        """Add a warning message to the validation result"""
        self.warnings.append(message)
        logger.warning(f"Validation warning: {message}")
    
    def add_info(self, message: str):
        """Add an info message to the validation result"""
        self.info.append(message)
        logger.info(f"Validation info: {message}")
        
    def clear_messages(self):
        """Clear all error, warning and info messages"""
        self.errors = []
        self.warnings = []
        self.info = []
        
    def has_errors(self) -> bool:
        """Check if validation has any errors"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """Check if validation has any warnings"""
        return len(self.warnings) > 0
    
    def get_validation_result(self) -> Dict[str, Any]:
        """Get complete validation result"""
        return {
            "is_valid": not self.has_errors(),
            "errors": self.errors,
            "warnings": self.warnings,
            "info": self.info
        }
    
    @staticmethod
    def is_in_range(value: float, min_val: float, max_val: float, 
                    inclusive: bool = True) -> bool:
        """Check if a value is within a specified range"""
        if inclusive:
            return min_val <= value <= max_val
        else:
            return min_val < value < max_val
    
    @staticmethod
    def is_positive(value: float) -> bool:
        """Check if a value is positive"""
        return value > 0
    
    @staticmethod
    def is_non_negative(value: float) -> bool:
        """Check if a value is non-negative"""
        return value >= 0