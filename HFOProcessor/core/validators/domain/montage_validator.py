"""Validator for montage configuration parameters"""

from typing import Dict, Any, <PERSON>, Tuple, Optional
from ..base_validator import BaseValida<PERSON>
from config.parameters import ParameterConfig


class MontageValidator(BaseValidator):
    """Validates montage configuration parameters"""
    
    def validate(self, montage: Dict[str, Any], 
                available_channels: Optional[List[str]] = None) -> Tuple[bool, List[str]]:
        """
        Validate montage configuration
        
        Args:
            montage: Dictionary with type and optional reference_channel
            available_channels: List of available channel labels (if known)
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        if 'type' not in montage:
            self.add_error("Missing required field: type")
            return False, self.errors
        
        montage_type = montage['type']
        valid_types = ParameterConfig.MONTAGE_CONFIG['types']
        
        if montage_type not in valid_types:
            self.add_error(f"Invalid montage type: {montage_type}. Valid types: {valid_types}")
            return False, self.errors
        
        # Validate referential montage requirements
        if montage_type == 'referential':
            self._validate_referential_montage(montage, available_channels)
        
        return not self.has_errors(), self.errors
    
    def _validate_referential_montage(self, montage: Dict[str, Any], 
                                    available_channels: Optional[List[str]]) -> None:
        """Validate referential montage specific requirements"""
        if 'reference_channel' not in montage:
            self.add_error("Referential montage requires 'reference_channel' field")
            return
        
        ref_channel = montage['reference_channel']
        
        if not isinstance(ref_channel, str):
            self.add_error(f"Invalid reference_channel type: expected string, got {type(ref_channel).__name__}")
            return
        
        if not ref_channel:
            self.add_error("Reference channel cannot be empty")
            return
        
        # Validate against available channels if provided
        if available_channels:
            if ref_channel not in available_channels:
                self.add_error(
                    f"Reference channel '{ref_channel}' not found in available channels. "
                    f"Available: {', '.join(available_channels[:5])}"
                    f"{' ...' if len(available_channels) > 5 else ''}"
                )
    
    def get_default_montage(self) -> Dict[str, Any]:
        """Get default montage configuration"""
        return {
            'type': ParameterConfig.MONTAGE_CONFIG['default'],
            'reference_channel': None
        }
    
    def get_montage_info(self) -> Dict[str, Any]:
        """Get information about available montage types"""
        return {
            'types': ParameterConfig.MONTAGE_CONFIG['types'],
            'default': ParameterConfig.MONTAGE_CONFIG['default'],
            'descriptions': {
                'bipolar': 'Sequential channel subtraction (Ch1-Ch2, Ch2-Ch3, etc.)',
                'average': 'Common average reference (each channel minus average of all)',
                'referential': 'Single reference channel (each channel minus reference)'
            }
        }
    
    def validate_channel_count_for_montage(self, montage_type: str, num_channels: int) -> Tuple[bool, str]:
        """
        Validate if the number of channels is sufficient for the montage type
        
        Args:
            montage_type: Type of montage
            num_channels: Number of available channels
            
        Returns:
            Tuple[bool, str]: (is_valid, error_message)
        """
        if montage_type == 'bipolar' and num_channels < 2:
            return False, "Bipolar montage requires at least 2 channels"
        
        if montage_type == 'average' and num_channels < 2:
            return False, "Average montage requires at least 2 channels"
        
        if montage_type == 'referential' and num_channels < 2:
            return False, "Referential montage requires at least 2 channels"
        
        return True, ""