import numpy as np
import os
import re
from typing import Dict, Any, <PERSON>, Tuple, Optional
from .base_validator import BaseValidator
from ..exceptions.validation_exceptions import EDFValidationError

class EDFValidator(BaseValidator):
    """Validator for EDF files and their headers"""
    
    # Minimum sampling rate for HFO detection (Hz)
    MIN_SAMPLING_RATE = 200
    
    # Required EDF header fields
    REQUIRED_HEADER_FIELDS = [
        'ver', 'patientID', 'recordID', 'startdate', 'starttime',
        'bytes', 'records', 'duration', 'ns', 'label', 'units',
        'physicalMin', 'physicalMax', 'digitalMin', 'digitalMax',
        'samples', 'frequency'
    ]
    
    # EDF version should be 0
    VALID_EDF_VERSION = 0
    
    def __init__(self):
        super().__init__()
        
    def validate_file(self, filepath: str) -> Tuple[bool, List[str]]:
        """
        Validate EDF file existence and format
        
        Args:
            filepath: Path to the EDF file
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        # Check file exists
        if not os.path.exists(filepath):
            self.add_error(f"File not found: {filepath}")
            return False, self.errors
        
        # Check file extension
        if not filepath.lower().endswith('.edf'):
            self.add_error(f"File must have .edf extension: {filepath}")
            return False, self.errors
        
        # Check file is readable
        if not os.access(filepath, os.R_OK):
            self.add_error(f"File is not readable: {filepath}")
            return False, self.errors
        
        # Check file size (EDF header is at least 256 bytes)
        file_size = os.path.getsize(filepath)
        if file_size < 256:
            self.add_error(f"File too small to be valid EDF (< 256 bytes): {file_size} bytes")
            return False, self.errors
        
        return not self.has_errors(), self.errors
    
    def validate_header(self, header: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        Validate EDF header structure and content
        
        Args:
            header: EDF header dictionary
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        self.clear_messages()
        
        # Check all required fields are present
        for field in self.REQUIRED_HEADER_FIELDS:
            if field not in header:
                self.add_error(f"Missing required header field: {field}")
        
        if self.has_errors():
            return False, self.errors
        
        # Validate EDF version
        if header['ver'] != self.VALID_EDF_VERSION:
            self.add_warning(f"Non-standard EDF version: {header['ver']} (expected {self.VALID_EDF_VERSION})")
        
        # Validate number of channels
        if header['ns'] <= 0:
            self.add_error(f"Invalid number of channels: {header['ns']}")
        
        # Validate number of records
        if header['records'] <= 0:
            self.add_error(f"Invalid number of records: {header['records']}")
        
        # Validate duration
        if header['duration'] <= 0:
            self.add_error(f"Invalid record duration: {header['duration']}")
        
        # Validate physical/digital ranges
        self._validate_signal_ranges(header)
        
        # Validate sampling rates
        self._validate_sampling_rates(header)
        
        # Validate date/time format
        self._validate_datetime(header)
        
        # Validate channel labels
        self._validate_channel_labels(header)
        
        return not self.has_errors(), self.errors
    
    def _validate_signal_ranges(self, header: Dict[str, Any]):
        """Validate physical and digital min/max ranges"""
        ns = header['ns']
        
        # Convert to numpy arrays if not already
        phys_min = np.asarray(header['physicalMin'])
        phys_max = np.asarray(header['physicalMax'])
        dig_min = np.asarray(header['digitalMin'])
        dig_max = np.asarray(header['digitalMax'])
        
        # Check array lengths
        if len(phys_min) != ns:
            self.add_error(f"Physical min array length ({len(phys_min)}) doesn't match number of channels ({ns})")
        if len(phys_max) != ns:
            self.add_error(f"Physical max array length ({len(phys_max)}) doesn't match number of channels ({ns})")
        if len(dig_min) != ns:
            self.add_error(f"Digital min array length ({len(dig_min)}) doesn't match number of channels ({ns})")
        if len(dig_max) != ns:
            self.add_error(f"Digital max array length ({len(dig_max)}) doesn't match number of channels ({ns})")
        
        # Check ranges are valid
        for i in range(min(ns, len(phys_min), len(phys_max))):
            if phys_min[i] >= phys_max[i]:
                self.add_error(f"Invalid physical range for channel {i}: min ({phys_min[i]}) >= max ({phys_max[i]})")
        
        for i in range(min(ns, len(dig_min), len(dig_max))):
            if dig_min[i] >= dig_max[i]:
                self.add_error(f"Invalid digital range for channel {i}: min ({dig_min[i]}) >= max ({dig_max[i]})")
            
            # Check for zero scaling factor
            if dig_max[i] - dig_min[i] == 0:
                self.add_error(f"Zero digital range for channel {i}: will cause division by zero in scaling")
    
    def _validate_sampling_rates(self, header: Dict[str, Any]):
        """Validate sampling rates for HFO detection"""
        freq = header.get('frequency')
        
        if freq is None:
            self.add_error("No frequency/sampling rate information in header")
            return
        
        # Convert to numpy array for consistent handling
        if isinstance(freq, (list, np.ndarray)):
            freq_array = np.asarray(freq)
        else:
            freq_array = np.array([freq])
        
        # Check minimum sampling rate for HFO detection
        min_rate = np.min(freq_array)
        if min_rate < self.MIN_SAMPLING_RATE:
            self.add_error(f"Sampling rate too low for HFO detection: {min_rate}Hz (minimum required: {self.MIN_SAMPLING_RATE}Hz)")
        
        # Check for consistency across channels
        if len(freq_array) > 1:
            unique_rates = np.unique(freq_array)
            if len(unique_rates) > 1:
                self.add_warning(f"Different sampling rates across channels: {unique_rates}Hz. Will use mean: {np.mean(freq_array):.2f}Hz")
        
        # Check for reasonable maximum (Nyquist considerations)
        max_rate = np.max(freq_array)
        if max_rate > 10000:
            self.add_warning(f"Very high sampling rate detected: {max_rate}Hz. This may impact performance.")
    
    def _validate_datetime(self, header: Dict[str, Any]):
        """Validate date and time format in header"""
        import re
        from datetime import datetime
        
        # Validate start date format (dd.mm.yy)
        startdate = header.get('startdate', '')
        if not re.match(r'^\d{2}\.\d{2}\.\d{2}$', startdate):
            self.add_warning(f"Non-standard date format: {startdate} (expected dd.mm.yy)")
        else:
            # Try to parse the date
            try:
                day, month, year = startdate.split('.')
                # Handle 2-digit year
                year_full = int('20' + year) if int(year) < 50 else int('19' + year)
                datetime(year_full, int(month), int(day))
            except (ValueError, Exception) as e:
                self.add_error(f"Invalid date: {startdate} - {str(e)}")
        
        # Validate start time format (hh.mm.ss)
        starttime = header.get('starttime', '')
        if not re.match(r'^\d{2}\.\d{2}\.\d{2}$', starttime):
            self.add_warning(f"Non-standard time format: {starttime} (expected hh.mm.ss)")
        else:
            # Try to parse the time
            try:
                hour, minute, second = starttime.split('.')
                if not (0 <= int(hour) < 24):
                    self.add_error(f"Invalid hour in time: {hour}")
                if not (0 <= int(minute) < 60):
                    self.add_error(f"Invalid minute in time: {minute}")
                if not (0 <= int(second) < 60):
                    self.add_error(f"Invalid second in time: {second}")
            except (ValueError, Exception) as e:
                self.add_error(f"Invalid time: {starttime} - {str(e)}")
    
    def _validate_channel_labels(self, header: Dict[str, Any]):
        """Validate channel labels"""
        labels = header.get('label', [])
        ns = header['ns']
        
        if len(labels) != ns:
            self.add_error(f"Number of labels ({len(labels)}) doesn't match number of channels ({ns})")
            return
        
        # Check for duplicate labels
        unique_labels = set()
        duplicates = []
        for label in labels:
            if label in unique_labels:
                duplicates.append(label)
            unique_labels.add(label)
        
        if duplicates:
            self.add_warning(f"Duplicate channel labels found: {duplicates}")
        
        # Check for empty labels
        empty_count = sum(1 for label in labels if not label or label.strip() == '')
        if empty_count > 0:
            self.add_error(f"Found {empty_count} empty channel labels")
        
        # Validate label format (should not contain certain special characters)
        invalid_labels = []
        for label in labels:
            if any(char in label for char in ['\\n', '\\r', '\\t']):
                invalid_labels.append(label)
        
        if invalid_labels:
            self.add_error(f"Invalid characters in channel labels: {invalid_labels}")
    
    def validate(self, data: Any) -> Tuple[bool, List[str]]:
        """
        Main validation method
        
        Args:
            data: Can be either a filepath (str) or header dict
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, error_messages)
        """
        if isinstance(data, str):
            # Validate file path
            return self.validate_file(data)
        elif isinstance(data, dict):
            # Validate header
            return self.validate_header(data)
        else:
            self.clear_messages()
            self.add_error(f"Invalid data type for validation: {type(data)}")
            return False, self.errors