FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    gfortran \
    libopenblas-dev \
    liblapack-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy HFO processing code from biormika-http-backend
# We'll need to copy the core HFO engine
COPY core/ ./core/
COPY services/ ./services/
COPY processor.py .

# Create directories for temporary file processing
RUN mkdir -p /tmp/edf_processing /tmp/results

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV LOG_LEVEL=INFO

# Run the processor
CMD ["python", "processor.py"]