"""
Main HFO processor for ECS Fargate.
Orchestrates HFO analysis jobs using service layer.
"""

import logging
import os
import time

from services import AWSService, JobManager, NotificationService

# Configure logging
logging.basicConfig(
    level=os.getenv("LOG_LEVEL", "INFO"),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class HFOProcessor:
    """Orchestrates HFO analysis processing using service layer"""

    def __init__(self):
        """Initialize processor with services"""
        # Initialize services
        self.aws_service = AWSService()
        self.notification_service = NotificationService(self.aws_service)
        self.job_manager = JobManager(self.aws_service, self.notification_service)

        # Validate environment
        self.validate_environment()
        logger.info("HFO Processor initialized successfully")

    def validate_environment(self):
        """Validate required environment variables"""
        is_valid, missing_vars = self.aws_service.validate_environment()
        if not is_valid:
            raise ValueError(f"Missing required environment variables: {missing_vars}")

    def run(self):
        """Main processing loop"""
        logger.info("Starting HFO processor...")

        # Configure polling parameters
        no_message_wait = 5  # Wait time when no messages
        error_wait = 10      # Wait time after errors

        while True:
            try:
                # Poll SQS for messages
                messages = self.aws_service.poll_sqs_queue(
                    max_messages=1,
                    visibility_timeout=3600,  # 1 hour
                    wait_time=20              # Long polling
                )

                if messages:
                    for message in messages:
                        self.process_message(message)
                else:
                    # No messages available
                    time.sleep(no_message_wait)

            except Exception as e:
                logger.error(f"Error in main processing loop: {e}")
                time.sleep(error_wait)

    def process_message(self, message: dict):
        """
        Process a single message from the queue

        Args:
            message: SQS message dictionary
        """
        try:
            # Delegate to job manager
            success = self.job_manager.process_job_message(message)

            if success:
                logger.info("Message processed successfully")
            else:
                logger.warning("Message processing failed but handled")

        except Exception as e:
            logger.error(f"Unhandled error processing message: {e}")
            # Message will remain in queue for retry or DLQ


def main():
    """Main entry point"""
    try:
        processor = HFOProcessor()
        processor.run()
    except Exception as e:
        logger.critical(f"Failed to start processor: {e}")
        raise


if __name__ == "__main__":
    main()