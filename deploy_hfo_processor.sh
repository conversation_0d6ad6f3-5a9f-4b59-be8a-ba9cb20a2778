#!/bin/bash

set -e

echo "🚀 Starting HFO Processor Deployment..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
AWS_PROFILE="biormika"
AWS_REGION="us-east-1"
ECR_REPOSITORY="biormika-hfo-processor"
AWS_ACCOUNT_ID="************"
ECR_URI="${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPOSITORY}"
# Match CDK-defined names: cluster_name="biormika-hfo-cluster", service_name="biormika-hfo-processor"
ECS_CLUSTER="biormika-hfo-cluster"
ECS_SERVICE="biormika-hfo-processor"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker.${NC}"
    exit 1
fi

echo "🔐 Logging into ECR..."
aws ecr get-login-password --region ${AWS_REGION} --profile ${AWS_PROFILE} | \
    docker login --username AWS --password-stdin ${ECR_URI}

echo "🏗️  Building Docker image..."
cd HFOProcessor

# Build for ARM64 architecture (for cost optimization on AWS)
docker build --platform linux/arm64 -t ${ECR_REPOSITORY} .

echo "🏷️  Tagging image..."
docker tag ${ECR_REPOSITORY}:latest ${ECR_URI}:latest

# Also tag with timestamp for versioning
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
docker tag ${ECR_REPOSITORY}:latest ${ECR_URI}:${TIMESTAMP}

echo "📤 Pushing image to ECR..."
docker push ${ECR_URI}:latest
docker push ${ECR_URI}:${TIMESTAMP}

echo -e "${GREEN}✅ Docker image pushed successfully!${NC}"

# Check if ECS service exists
if aws ecs describe-services --cluster ${ECS_CLUSTER} --services ${ECS_SERVICE} --profile ${AWS_PROFILE} --region ${AWS_REGION} 2>/dev/null | grep -q "serviceArn"; then
    echo "🔄 Updating ECS service to use new image..."

    # Force new deployment to pull the latest image
    aws ecs update-service \
        --cluster ${ECS_CLUSTER} \
        --service ${ECS_SERVICE} \
        --force-new-deployment \
        --profile ${AWS_PROFILE} \
        --region ${AWS_REGION} \
        --output json > /dev/null

    echo -e "${YELLOW}⏳ ECS service update initiated. The service will gradually replace tasks with the new image.${NC}"
    echo "📊 You can monitor the deployment progress in the AWS Console:"
    echo "   https://console.aws.amazon.com/ecs/v2/clusters/${ECS_CLUSTER}/services/${ECS_SERVICE}"

    # Optional: Wait for service to stabilize
    echo "⏳ Waiting for service to stabilize (this may take a few minutes)..."
    aws ecs wait services-stable \
        --cluster ${ECS_CLUSTER} \
        --services ${ECS_SERVICE} \
        --profile ${AWS_PROFILE} \
        --region ${AWS_REGION} 2>/dev/null || {
        echo -e "${YELLOW}⚠️  Timeout waiting for service to stabilize. Check AWS Console for status.${NC}"
    }
else
    echo -e "${YELLOW}⚠️  ECS service not found. Make sure infrastructure is deployed first with deploy_infra.sh${NC}"
    echo "   The infrastructure deployment will create the ECS service using this Docker image."
fi

# Show recent tasks status
echo ""
echo "📋 Recent ECS Tasks Status:"
aws ecs list-tasks \
    --cluster ${ECS_CLUSTER} \
    --service-name ${ECS_SERVICE} \
    --profile ${AWS_PROFILE} \
    --region ${AWS_REGION} \
    --output json 2>/dev/null | jq -r '.taskArns[]' | while read task_arn; do
    if [ ! -z "$task_arn" ]; then
        task_info=$(aws ecs describe-tasks \
            --cluster ${ECS_CLUSTER} \
            --tasks "$task_arn" \
            --profile ${AWS_PROFILE} \
            --region ${AWS_REGION} \
            --output json 2>/dev/null | jq -r '.tasks[0] | "\(.lastStatus) - Started: \(.startedAt // "N/A")"')
        echo "   Task: $task_info"
    fi
done

echo ""
echo -e "${GREEN}✅ HFO Processor deployment completed successfully!${NC}"
echo ""
echo "📝 Next steps:"
echo "   1. Monitor ECS tasks in AWS Console"
echo "   2. Check CloudWatch logs for any errors"
echo "   3. Submit a test analysis job to verify the processor is working"