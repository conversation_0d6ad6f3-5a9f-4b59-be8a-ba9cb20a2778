#!/bin/bash

# Deploy Backend Script
# Builds Lambda package and deploys via CDK

set -e

echo "🚀 Starting Backend Deployment..."
echo "=================================="

# Check if running from root directory
if [ ! -d "Backend" ]; then
    echo "❌ Error: Must run from project root directory"
    exit 1
fi

# Navigate to Backend directory
cd Backend

# Build Lambda deployment package
echo "📦 Building Lambda deployment package..."
echo ""

# Check if build script exists
if [ ! -f "build_lambda.sh" ]; then
    echo "❌ Error: build_lambda.sh not found"
    exit 1
fi

# Make build script executable
chmod +x build_lambda.sh

# Run build script
./build_lambda.sh

# Check if package was created
if [ ! -f "lambda_deployment.zip" ]; then
    echo "❌ Error: Lambda package build failed"
    exit 1
fi

echo ""
echo "✅ Lambda package built successfully"

# Return to root
cd ..

# Navigate to Infrastructure directory
cd Infra

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Install/update dependencies (quietly)
pip install -q -r requirements.txt -r requirements-dev.txt

echo ""
echo "🚀 Deploying backend to AWS Lambda..."
echo ""

# Deploy with auto-approval (backend only updates Lambda)
cdk deploy --profile biormika --outputs-file cdk-outputs.json --require-approval never

echo ""
echo "✅ Backend deployment complete!"
echo ""

# Extract Lambda function name
if [ -f "cdk-outputs.json" ]; then
    FUNCTION_ARN=$(grep "LambdaFunctionArn" cdk-outputs.json | cut -d'"' -f4)
    if [ ! -z "$FUNCTION_ARN" ]; then
        FUNCTION_NAME=$(echo $FUNCTION_ARN | rev | cut -d':' -f1 | rev)
        echo "📋 Lambda Function: $FUNCTION_NAME"
        echo ""

        # Test the Lambda function
        echo "🧪 Testing Lambda health endpoint..."
        API_URL=$(grep "ApiGatewayUrl" cdk-outputs.json | cut -d'"' -f4)
        if [ ! -z "$API_URL" ]; then
            HEALTH_RESPONSE=$(curl -s "${API_URL}health" || echo "failed")
            if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
                echo "✅ API is healthy: $HEALTH_RESPONSE"
            else
                echo "⚠️  Health check failed. Check CloudWatch logs:"
                echo "   aws logs tail /aws/lambda/$FUNCTION_NAME --profile biormika --follow"
            fi
        fi
    fi
fi

# Return to root
cd ..