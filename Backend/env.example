# AWS Configuration
AWS_PROFILE=biormika
AWS_REGION=us-east-1

# S3 Configuration
# Replace with your actual bucket name from CDK deployment
# Example from your deployment: biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv
S3_BUCKET_NAME=biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:5173","http://localhost:3000"]

# File Upload Configuration
MAX_FILE_SIZE_MB=1024
ALLOWED_FILE_EXTENSIONS=[".edf",".EDF"]

# Presigned URL Configuration
PRESIGNED_URL_EXPIRY_SECONDS=3600
