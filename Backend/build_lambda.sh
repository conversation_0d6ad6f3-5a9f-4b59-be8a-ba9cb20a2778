#!/bin/bash

set -e

echo "Building Lambda deployment package..."

# Clean up previous build
rm -rf lambda_package
rm -f lambda_deployment.zip

# Create package directory
mkdir -p lambda_package

# Install dependencies using pip for Lambda's Python runtime (3.11) on ARM64
echo "Installing dependencies for Lambda (ARM64)..."
pip install \
    --platform manylinux2014_aarch64 \
    --implementation cp \
    --python-version 3.11 \
    --only-binary=:all: \
    --target lambda_package \
    -r requirements.txt

# Copy application code
echo "Copying application code..."
cp -r app lambda_package/
cp lambda_handler.py lambda_package/

# Create deployment package
echo "Creating deployment package..."
cd lambda_package
zip -r ../lambda_deployment.zip . -x "*.pyc" "*.pyo" "*__pycache__*" -q
cd ..

echo "Lambda deployment package created: lambda_deployment.zip"
echo "Size: $(du -h lambda_deployment.zip | cut -f1)"