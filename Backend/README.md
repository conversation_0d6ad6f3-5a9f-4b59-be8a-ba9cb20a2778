# Backend - API Orchestration Service

## What This Does

The Backend is the central coordinator of the Biormika platform. It's a FastAPI service running on AWS Lambda that manages the entire workflow from file upload to result delivery.

### Primary Responsibilities

1. **File Management**: Handle large EDF uploads (up to 1GB) using multipart upload to S3
2. **Job Orchestration**: Queue analysis jobs and track their progress
3. **Status Tracking**: Maintain job state in DynamoDB with TTL for cleanup
4. **Result Delivery**: Provide processed HFO detection results to frontend

### Why It Exists

The frontend can't directly access AWS services (security), and the HFO processor needs a way to receive jobs. The Backend bridges this gap by providing a secure REST API that coordinates all components.

## Architecture

```
Frontend → API Gateway → Lambda Function → FastAPI
                              ↓
                    ┌─────────┼─────────┐
                    S3      SQS      DynamoDB
                 (Storage) (Queue)  (Tracking)
```

## Quick Start

### Local Development

```bash
# Setup
python3 -m venv venv && source venv/bin/activate
pip install -r requirements.txt

# Configure (get values from CDK output)
cp .env.example .env
# Edit .env with your S3 bucket and CloudFront URL

# Run
python run.py  # http://localhost:8000/docs
```

### Essential Commands

```bash
# Development
python run.py                    # Start local server
pytest tests/                    # Run tests

# Deployment
./build_lambda.sh               # Create deployment package
./deploy_backend.sh              # Deploy to Lambda

# Debugging
aws lambda invoke \
  --function-name BiormikaStack-ApiFunction \
  --payload '{"path": "/api/v1/files/list"}' \
  --profile biormika response.json

# View logs
aws logs tail /aws/lambda/BiormikaStack-ApiFunction \
  --profile biormika --follow
```

## API Endpoints

### File Operations
| Endpoint | Purpose | Example |
|----------|---------|---------|
| `POST /files/multipart/initiate` | Start large upload | Returns upload_id, S3 key |
| `POST /files/multipart/batch-generate-presigned-urls` | Get part URLs | For parallel chunk uploads |
| `POST /files/multipart/complete` | Finish upload | Combines all parts |
| `GET /files/list` | List EDF files | Returns file metadata |
| `DELETE /files/delete` | Remove file | Deletes from S3 |

### Analysis Management
| Endpoint | Purpose | Example |
|----------|---------|---------|
| `POST /analysis/submit` | Start HFO detection | Queues job, returns job_id |
| `POST /analysis/batch-submit` | Multiple files | Process several EDFs |
| `GET /analysis/jobs` | List all jobs | With status filters |
| `GET /analysis/jobs/{id}/results` | Get results | Returns CSV/JSON data |

### User Preferences
| Endpoint | Purpose | Example |
|----------|---------|---------|
| `GET /user/preferences` | Get settings | Detection parameters |
| `PUT /user/preferences` | Update settings | Email, thresholds |

## How Multipart Upload Works

For files >100MB, we use S3 multipart upload:

1. **Initiate**: Create upload session, get upload_id
2. **Generate URLs**: Get presigned URLs for each 10MB chunk
3. **Upload Parts**: Frontend uploads chunks in parallel
4. **Complete**: Combine parts into final file

```python
# Frontend sends
POST /files/multipart/initiate
{
  "file_name": "patient_recording.edf",
  "file_size": 524288000  # 500MB
}

# Backend returns
{
  "upload_id": "abc123",
  "key": "uploads/uuid/patient_recording.edf"
}
```

## Job Processing Flow

```python
# 1. Submit job
POST /analysis/submit
{
  "file_key": "uploads/uuid/recording.edf",
  "parameters": {
    "frequency_band": [80, 500],
    "threshold": 3.0
  },
  "email": "<EMAIL>"
}

# 2. Backend creates job
- Generates unique job_id
- Saves to DynamoDB with "queued" status
- Sends message to SQS FIFO queue

# 3. Returns immediately
{
  "job_id": "550e8400-e29b",
  "status": "queued"
}

# 4. HFO Processor picks up job
# 5. Results saved to S3
# 6. Status updated to "completed"
```

## Configuration

### Environment Variables
```env
# Required
S3_BUCKET_NAME=biormika-stack-files-xxxxx
CLOUDFRONT_URL=https://dxxxxx.cloudfront.net

# Optional (defaults in code)
SQS_QUEUE_URL=https://sqs.region.amazonaws.com/xxx
DYNAMODB_TABLE_NAME=BiormikaHFOJobTable
AWS_REGION=us-east-1
```

### Lambda Settings
- **Runtime**: Python 3.9 ARM64
- **Memory**: 3008 MB
- **Timeout**: 900 seconds (15 minutes)
- **Handler**: lambda_handler.handler

### CORS Configuration
```python
origins = [
    "http://localhost:5173",  # Local frontend
    "http://localhost:3000",  # Alternative port
    CLOUDFRONT_URL            # Production
]
```

## Project Structure

```
Backend/
├── app/
│   ├── routers/         # API endpoints
│   │   ├── files.py     # S3 operations
│   │   ├── analysis.py  # Job management
│   │   └── user.py      # Preferences
│   ├── services/        # Business logic
│   │   ├── aws_clients.py  # Boto3 singletons
│   │   ├── file_service.py # S3 operations
│   │   └── job_service.py  # Queue/DB operations
│   └── models/          # Pydantic schemas
├── lambda_handler.py    # Lambda entry point
├── run.py              # Local dev server
└── build_lambda.sh     # Package builder
```

## Error Handling

| Error | Cause | Solution |
|-------|-------|----------|
| 413 | File too large | Use multipart for >100MB |
| 403 | S3 access denied | Check IAM role permissions |
| 504 | Lambda timeout | Increase timeout or optimize |
| 400 | Invalid file | Must be .edf extension |

## Performance Tips

- Use multipart upload for files >100MB
- Presigned URLs cached for 1 hour
- Connection pooling for AWS clients
- DynamoDB TTL cleans old jobs after 30 days

## Testing

```bash
# Run all tests
pytest

# Test specific endpoint
curl -X GET http://localhost:8000/api/v1/files/list

# Test with sample file
curl -X POST http://localhost:8000/api/v1/analysis/submit \
  -H "Content-Type: application/json" \
  -d '{"file_key": "test.edf", "parameters": {}}'
```

## Dependencies

- **FastAPI**: Modern Python web framework
- **Mangum**: AWS Lambda adapter
- **Boto3**: AWS SDK
- **Pydantic**: Data validation
- **Python-dotenv**: Environment management

## License

Copyright 2024 Biormika. All rights reserved.