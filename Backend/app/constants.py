# AWS Configuration
AWS_PROFILE = "biormika"
AWS_REGION = "us-east-1"

# File Configuration
MAX_FILE_SIZE_MB = 1328  # 1.3GB
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
ALLOWED_EXTENSIONS = (".edf", ".EDF")
ALLOWED_FILE_EXTENSIONS = [".edf", ".EDF"]
EDF_EXTENSION = ".edf"
EDF_FILES_PREFIX = "edf-files"

# S3 Configuration
S3_PREFIX = "edf-files/"
DEFAULT_EXPIRY_SECONDS = 3600
PRESIGNED_URL_EXPIRY_SECONDS = 3600
CONTENT_TYPE_OCTET_STREAM = "application/octet-stream"
METADATA_ORIGINAL_FILENAME = "original-filename"
S3_CLIENT_METHOD_PUT = "put_object"
S3_CLIENT_METHOD_GET = "get_object"
S3_CLIENT_METHOD_UPLOAD_PART = "upload_part"
S3_LIST_OBJECTS_METHOD = "list_objects_v2"
S3_ERROR_NOT_FOUND = "404"
S3_CORS_MAX_AGE_SECONDS = 3600
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]

# API Configuration
API_PREFIX = "/api/v1"
API_TITLE = "Biormika EDF File Upload API"
API_DESCRIPTION = "API for uploading and managing EDF files for HFO analysis"
API_VERSION = "1.0.0"

# Network Configuration
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS Configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_HEADERS = ["*"]

# Logging Configuration
LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOGGING_LEVEL = "INFO"

# HTTP Status Codes
HTTP_STATUS_OK = 200
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_INTERNAL_SERVER_ERROR = 500

# Multipart Upload Configuration
MULTIPART_MIN_FILE_SIZE = 5 * 1024 * 1024  # 5MB - Use multipart for files larger than this
MULTIPART_MIN_PART_SIZE = 5 * 1024 * 1024  # 5MB minimum part size
MULTIPART_MAX_PART_SIZE = 100 * 1024 * 1024  # 100MB maximum part size
MULTIPART_DEFAULT_PART_SIZE = 10 * 1024 * 1024  # 10MB default part size
MULTIPART_MAX_PARTS = 10000  # AWS S3 limit
MULTIPART_URL_EXPIRY_SECONDS = 3600  # 1 hour
MULTIPART_MAX_RETRIES = 3
MULTIPART_RETRY_DELAY_SECONDS = 0.5
MULTIPART_MAX_RETRY_DELAY_SECONDS = 8.0
MULTIPART_BACKOFF_MULTIPLIER = 2

# Multipart Dynamic Sizing Configuration
MULTIPART_SMALL_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB
MULTIPART_MEDIUM_FILE_THRESHOLD = 500 * 1024 * 1024  # 500MB
MULTIPART_SMALL_FILE_CHUNK_SIZE = 5 * 1024 * 1024  # 5MB chunks for <100MB
MULTIPART_MEDIUM_FILE_CHUNK_SIZE = 10 * 1024 * 1024  # 10MB chunks for 100-500MB
MULTIPART_LARGE_FILE_CHUNK_SIZE = 20 * 1024 * 1024  # 20MB chunks for >500MB

# Multipart Concurrency Configuration
MULTIPART_SMALL_FILE_CONCURRENCY = 3  # 3 parallel uploads for <100MB
MULTIPART_MEDIUM_FILE_CONCURRENCY = 5  # 5 parallel uploads for 100-500MB
MULTIPART_LARGE_FILE_CONCURRENCY = 8  # 8 parallel uploads for >500MB
MULTIPART_MAX_CONCURRENCY = 10  # Maximum concurrent part uploads

# Lambda Configuration
LAMBDA_RESPONSE_HEADERS = {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
}
