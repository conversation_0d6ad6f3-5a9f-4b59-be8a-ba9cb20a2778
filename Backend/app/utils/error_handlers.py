
from fastapi import HTTPException, status

from ..logging_config import get_logger

logger = get_logger(__name__)


class ErrorResponseFactory:
    @staticmethod
    def _raise_http_exception(
        message: str,
        status_code: int,
        log_level: str = "error",
        exc_info: bool = False,
    ):
        log_method = getattr(logger, log_level)
        log_method(message, exc_info=exc_info)
        raise HTTPException(status_code=status_code, detail=message)

    @classmethod
    def server_error(
        cls,
        operation: str,
        error: Exception,
        context: str | None = None,
    ):
        message = f"Failed to {operation}"
        if context:
            message += f" for {context}"
        message += f": {str(error)}"
        cls._raise_http_exception(
            message,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
            exc_info=True,
        )

    @classmethod
    def not_found(cls, resource: str, identifier: str):
        message = f"{resource} not found: {identifier}"
        cls._raise_http_exception(
            message,
            status.HTTP_404_NOT_FOUND,
            log_level="warning",
        )

    @classmethod
    def bad_request(cls, message: str):
        cls._raise_http_exception(
            message,
            status.HTTP_400_BAD_REQUEST,
            log_level="warning",
        )


def handle_s3_error(operation, error, key=None):
    return ErrorResponseFactory.server_error(operation, error, key)


def handle_not_found(resource, identifier):
    return ErrorResponseFactory.not_found(resource, identifier)
