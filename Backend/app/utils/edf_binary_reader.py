"""
Binary EDF reader for robust channel extraction from EDF files.
Based on the official EDF specification, reads headers directly from binary structure.
"""

import re
from typing import Dict, List, Optional


def extract_edf_metadata(filepath: str, target_signals: Optional[List[str]] = None) -> Dict:
    """
    Direct binary reading of EDF header to extract metadata.
    Works with both continuous and discontinuous EDF files.

    Args:
        filepath: Path to the EDF file
        target_signals: Optional list of specific signals to extract

    Returns:
        Dictionary containing EDF header information including channel labels
    """
    try:
        with open(filepath, 'rb') as fid:
            # Read header according to EDF specification
            hdr = {}

            # Version (8 bytes)
            version = fid.read(8).decode('ascii', errors='replace').strip()
            hdr['ver'] = int(version) if version.isdigit() else 0

            # Patient ID (80 bytes)
            hdr['patientID'] = fid.read(80).decode('ascii', errors='replace').strip()

            # Recording ID (80 bytes)
            hdr['recordID'] = fid.read(80).decode('ascii', errors='replace').strip()

            # Start date (8 bytes) - dd.mm.yy format
            hdr['startdate'] = fid.read(8).decode('ascii', errors='replace').strip()

            # Start time (8 bytes) - hh.mm.ss format
            hdr['starttime'] = fid.read(8).decode('ascii', errors='replace').strip()

            # Header bytes (8 bytes)
            header_bytes = fid.read(8).decode('ascii', errors='replace').strip()
            hdr['bytes'] = int(header_bytes) if header_bytes.isdigit() else 256

            # Reserved (44 bytes)
            fid.read(44)

            # Number of records (8 bytes)
            num_records = fid.read(8).decode('ascii', errors='replace').strip()
            hdr['records'] = int(num_records) if num_records.isdigit() else 1

            # Duration of a record in seconds (8 bytes)
            duration = fid.read(8).decode('ascii', errors='replace').strip()
            try:
                hdr['duration'] = float(duration)
            except ValueError:
                hdr['duration'] = 1.0

            # Number of signals (4 bytes)
            num_signals = fid.read(4).decode('ascii', errors='replace').strip()
            hdr['ns'] = int(num_signals) if num_signals.isdigit() else 0

            # If no signals, return empty structure
            if hdr['ns'] == 0:
                hdr['label'] = []
                hdr['frequency'] = []
                return hdr

            # Read signal labels (16 bytes each)
            hdr['label'] = []
            for _ in range(hdr['ns']):
                # Remove trailing whitespace and non-alphanumeric characters
                label = fid.read(16).decode('ascii', errors='replace').strip()
                # Clean up the label - remove special characters at the end
                label = re.sub(r'\W+$', '', label)
                hdr['label'].append(label)

            # Transducer type (80 bytes each)
            hdr['transducer'] = []
            for _ in range(hdr['ns']):
                hdr['transducer'].append(fid.read(80).decode('ascii', errors='replace').strip())

            # Physical dimension (8 bytes each)
            hdr['units'] = []
            for _ in range(hdr['ns']):
                hdr['units'].append(fid.read(8).decode('ascii', errors='replace').strip())

            # Physical minimum (8 bytes each)
            hdr['physicalMin'] = []
            for _ in range(hdr['ns']):
                val = fid.read(8).decode('ascii', errors='replace').strip()
                try:
                    hdr['physicalMin'].append(float(val))
                except ValueError:
                    hdr['physicalMin'].append(-32768.0)

            # Physical maximum (8 bytes each)
            hdr['physicalMax'] = []
            for _ in range(hdr['ns']):
                val = fid.read(8).decode('ascii', errors='replace').strip()
                try:
                    hdr['physicalMax'].append(float(val))
                except ValueError:
                    hdr['physicalMax'].append(32767.0)

            # Digital minimum (8 bytes each)
            hdr['digitalMin'] = []
            for _ in range(hdr['ns']):
                val = fid.read(8).decode('ascii', errors='replace').strip()
                try:
                    hdr['digitalMin'].append(int(val))
                except ValueError:
                    hdr['digitalMin'].append(-32768)

            # Digital maximum (8 bytes each)
            hdr['digitalMax'] = []
            for _ in range(hdr['ns']):
                val = fid.read(8).decode('ascii', errors='replace').strip()
                try:
                    hdr['digitalMax'].append(int(val))
                except ValueError:
                    hdr['digitalMax'].append(32767)

            # Prefiltering (80 bytes each)
            hdr['prefilter'] = []
            for _ in range(hdr['ns']):
                hdr['prefilter'].append(fid.read(80).decode('ascii', errors='replace').strip())

            # Number of samples per record (8 bytes each)
            hdr['samples'] = []
            for _ in range(hdr['ns']):
                val = fid.read(8).decode('ascii', errors='replace').strip()
                try:
                    hdr['samples'].append(int(val))
                except ValueError:
                    hdr['samples'].append(256)

            # Skip reserved area (32 bytes per signal)
            for _ in range(hdr['ns']):
                fid.read(32)

            # Calculate sampling frequency for each signal
            hdr['frequency'] = []
            for i in range(hdr['ns']):
                if hdr['duration'] > 0:
                    hdr['frequency'].append(hdr['samples'][i] / hdr['duration'])
                else:
                    hdr['frequency'].append(256.0)  # Default sampling rate

            # Handle target signal selection if specified
            if target_signals is not None:
                selected_indices = []
                for i, label in enumerate(hdr['label']):
                    if label in target_signals:
                        selected_indices.append(i)

                if selected_indices:
                    # Filter all arrays to selected signals only
                    hdr['label'] = [hdr['label'][i] for i in selected_indices]
                    hdr['units'] = [hdr['units'][i] for i in selected_indices]
                    hdr['physicalMin'] = [hdr['physicalMin'][i] for i in selected_indices]
                    hdr['physicalMax'] = [hdr['physicalMax'][i] for i in selected_indices]
                    hdr['digitalMin'] = [hdr['digitalMin'][i] for i in selected_indices]
                    hdr['digitalMax'] = [hdr['digitalMax'][i] for i in selected_indices]
                    hdr['prefilter'] = [hdr['prefilter'][i] for i in selected_indices]
                    hdr['transducer'] = [hdr['transducer'][i] for i in selected_indices]
                    hdr['samples'] = [hdr['samples'][i] for i in selected_indices]
                    hdr['frequency'] = [hdr['frequency'][i] for i in selected_indices]

            return hdr

    except Exception as e:
        # Return a default structure on error
        return {
            'label': [],
            'frequency': [],
            'records': 0,
            'duration': 0,
            'startdate': '',
            'starttime': '',
            'ns': 0,
            'error': str(e)
        }


def get_edf_channels(filepath: str) -> List[str]:
    """
    Simple function to extract just the channel labels from an EDF file.

    Args:
        filepath: Path to the EDF file

    Returns:
        List of channel labels
    """
    metadata = extract_edf_metadata(filepath)
    return metadata.get('label', [])


def get_edf_info(filepath: str) -> Dict:
    """
    Extract basic information needed for the frontend.

    Args:
        filepath: Path to the EDF file

    Returns:
        Dictionary with channels, sampling_rate, duration, etc.
    """
    metadata = extract_edf_metadata(filepath)

    # Get the most common sampling rate (in case channels have different rates)
    if metadata.get('frequency'):
        sampling_rate = int(metadata['frequency'][0]) if metadata['frequency'] else 256
    else:
        sampling_rate = 256

    # Calculate total duration
    duration = metadata.get('records', 0) * metadata.get('duration', 0)

    # Format datetime if available
    start_datetime = None
    if metadata.get('startdate') and metadata.get('starttime'):
        # Try to combine date and time
        try:
            start_datetime = f"{metadata['startdate']} {metadata['starttime']}"
        except:
            start_datetime = None

    return {
        'channels': metadata.get('label', []),
        'sampling_rate': sampling_rate,
        'duration_seconds': duration,
        'start_datetime': start_datetime,
        'num_channels': len(metadata.get('label', [])),
        'error': metadata.get('error')
    }