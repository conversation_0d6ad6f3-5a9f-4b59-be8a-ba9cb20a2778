import logging
import sys

from .constants import LOG<PERSON>NG_FORMAT, LOGGING_LEVEL


def setup_logging(name: str = __name__) -> logging.Logger:
    """Setup and return a configured logger instance"""
    logger = logging.getLogger(name)

    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter(LOGGING_FORMAT))
        logger.addHandler(handler)
        logger.setLevel(getattr(logging, LOGGING_LEVEL))

    return logger


def get_logger(name: str = __name__) -> logging.Logger:
    """Get a logger instance with the standard configuration"""
    return setup_logging(name)
