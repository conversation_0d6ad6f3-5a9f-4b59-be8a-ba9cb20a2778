from ..constants import CONTENT_TYPE_OCTET_STREAM, METADATA_ORIGINAL_FILENAME


class MultipartService:
    def __init__(self, s3_client, bucket_name: str, url_service):
        self.s3_client = s3_client
        self.bucket_name = bucket_name
        self.url_service = url_service

    def initiate_multipart_upload(self, key: str, filename: str) -> str:
        response = self.s3_client.create_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            ContentType=CONTENT_TYPE_OCTET_STREAM,
            Metadata={METADATA_ORIGINAL_FILENAME: filename},
        )
        return response["UploadId"]

    def generate_part_url(self, key: str, upload_id: str, part_number: int) -> str:
        return self.url_service.generate_multipart_part_url(key, upload_id, part_number)

    def complete_multipart_upload(self, key: str, upload_id: str, parts: list[dict]):
        self.s3_client.complete_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            UploadId=upload_id,
            MultipartUpload={"Parts": parts},
        )

    def abort_multipart_upload(self, key: str, upload_id: str):
        self.s3_client.abort_multipart_upload(
            Bucket=self.bucket_name,
            Key=key,
            UploadId=upload_id,
        )
