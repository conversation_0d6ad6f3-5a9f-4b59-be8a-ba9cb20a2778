"""
Service for managing HFO analysis jobs.
"""

import json
import os
import uuid
from datetime import datetime

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)


class JobService:
    """Service for managing analysis jobs."""

    def __init__(self):
        self.aws = get_aws_clients()

    async def create_job(
        self,
        file_key: str,
        user_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
        sender_email: str | None = None
    ) -> str:
        """Create a new analysis job."""
        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()
        sender = sender_email or os.getenv("SES_SENDER_EMAIL", "<EMAIL>")

        job_item = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": user_email,
            "receiver_email": user_email,
            "sender_email": sender,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": parameters or {},
        }

        if batch_id:
            job_item["batch_id"] = batch_id

        # Save to DynamoDB
        if self.aws.jobs_table:
            self.aws.jobs_table.put_item(Item=job_item)

        logger.info(f"Created job: {job_id}")
        return job_id

    async def send_to_queue(
        self,
        job_id: str,
        file_key: str,
        user_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
        sender_email: str | None = None
    ):
        """Send job to SQS queue for processing."""
        sender = sender_email or os.getenv("SES_SENDER_EMAIL", "<EMAIL>")
        message = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": user_email,
            "receiver_email": user_email,
            "sender_email": sender,
            "parameters": parameters or {},
        }

        if batch_id:
            message["batch_id"] = batch_id

        if self.aws.sqs_queue_url:
            self.aws.sqs.send_message(
                QueueUrl=self.aws.sqs_queue_url,
                MessageBody=json.dumps(message),
            )

    async def send_batch_to_queue(self, messages: list[dict]):
        """Send multiple jobs to SQS queue."""
        if not self.aws.sqs_queue_url or not messages:
            return

        # SQS batch limit is 10
        for i in range(0, len(messages), 10):
            batch = messages[i:i+10]
            self.aws.sqs.send_message_batch(
                QueueUrl=self.aws.sqs_queue_url,
                Entries=batch,
            )

    async def get_job_status(self, job_id: str) -> dict | None:
        """Get job status from DynamoDB."""
        if not self.aws.jobs_table:
            return None

        response = self.aws.jobs_table.get_item(Key={"job_id": job_id})
        return response.get("Item")

    async def get_batch_jobs(self, batch_id: str) -> list[dict]:
        """Get all jobs in a batch."""
        if not self.aws.jobs_table:
            return []

        response = self.aws.jobs_table.query(
            IndexName="BatchJobIndex",
            KeyConditionExpression="batch_id = :batch_id",
            ExpressionAttributeValues={":batch_id": batch_id},
        )
        return response.get("Items", [])

    def validate_file_exists(self, file_key: str):
        """Validate that file exists in S3."""
        try:
            self.aws.s3.head_object(
                Bucket=self.aws.s3_bucket_name,
                Key=file_key
            )
        except ClientError:
            raise ValueError(f"File not found: {file_key}")

    async def get_user_email(self, user_id: str) -> tuple[str, str]:
        """Get sender and receiver emails from preferences table."""
        try:
            if self.aws.preferences_table:
                response = self.aws.preferences_table.get_item(
                    Key={"user_id": user_id}
                )
                if "Item" in response:
                    item = response["Item"]
                    sender = item.get("sender_email") or item.get("email") or os.getenv(
                        "SES_SENDER_EMAIL", "<EMAIL>")
                    receiver = item.get("receiver_email") or item.get(
                        "email") or os.getenv("DEFAULT_RECEIVER_EMAIL", "<EMAIL>")
                    return sender, receiver
        except Exception as e:
            logger.warning(f"Error getting user email: {e}")

        return (
            os.getenv("SES_SENDER_EMAIL", "<EMAIL>"),
            os.getenv("DEFAULT_RECEIVER_EMAIL", "<EMAIL>")
        )
