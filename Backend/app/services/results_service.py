"""
Service for managing analysis results.
"""

import json

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)

# Default mock results for demonstration
MOCK_RESULTS = {
    "job-1": {
        "metadata": {
            "filename": "sample1.edf",
            "sampling_rate": 256,
            "duration_seconds": 600,
            "channels": [
                "FP1-F7", "F7-T3", "T3-T5", "T5-O1",  # Left temporal chain
                "FP2-F8", "F8-T4", "T4-T6", "T6-O2",  # Right temporal chain
                "FP1-F3", "F3-C3", "C3-P3", "P3-O1",  # Left parasagittal chain
                "FP2-F4", "F4-C4", "C4-P4", "P4-O2",  # Right parasagittal chain
                "FZ-CZ", "CZ-PZ",  # Midline chain
                "CH1-CH2", "CH2-CH3", "CH3-CH4", "CH4-CH5",  # Generic channels
                "CH5-CH6", "CH6-CH7", "CH7-CH8", "CH8-CH9", "CH9-CH10"
            ],
            "processing_time": 45.2
        },
        "statistics": {
            "total_hfos": 127,
            "hfo_density": 12.7,
            "channels_with_hfos": ["F7-T3", "T3-T5", "F8-T4", "T4-T6", "F3-C3", "C3-P3", "CH1-CH2", "CH2-CH3"],
            "hfo_rate_per_channel": {
                "FP1-F7": 0, "F7-T3": 15, "T3-T5": 18, "T5-O1": 0,
                "FP2-F8": 0, "F8-T4": 12, "T4-T6": 14, "T6-O2": 0,
                "FP1-F3": 0, "F3-C3": 20, "C3-P3": 22, "P3-O1": 0,
                "FP2-F4": 0, "F4-C4": 8, "C4-P4": 10, "P4-O2": 0,
                "FZ-CZ": 0, "CZ-PZ": 0,
                "CH1-CH2": 12, "CH2-CH3": 15, "CH3-CH4": 8, "CH4-CH5": 5,
                "CH5-CH6": 3, "CH6-CH7": 0, "CH7-CH8": 0, "CH8-CH9": 0, "CH9-CH10": 0
            }
        }
    }
}


class ResultsService:
    """Service for managing analysis results."""

    def __init__(self):
        self.aws = get_aws_clients()

    def _get_mock_channel_data(self) -> dict:
        """Generate realistic full-length mock channel data."""
        import numpy as np

        # Updated channels to match typical EDF file format and HFO processor mock data
        channels = [
            "FP1-F7", "F7-T3", "T3-T5", "T5-O1",  # Left temporal chain
            "FP2-F8", "F8-T4", "T4-T6", "T6-O2",  # Right temporal chain
            "FP1-F3", "F3-C3", "C3-P3", "P3-O1",  # Left parasagittal chain
            "FP2-F4", "F4-C4", "C4-P4", "P4-O2",  # Right parasagittal chain
            "FZ-CZ", "CZ-PZ",  # Midline chain
            "CH1-CH2", "CH2-CH3", "CH3-CH4", "CH4-CH5",  # Generic channels like in EDF files
            "CH5-CH6", "CH6-CH7", "CH7-CH8", "CH8-CH9", "CH9-CH10"
        ]
        sampling_rate = 256
        duration_seconds = 600  # 10 minutes of data
        num_samples = sampling_rate * duration_seconds  # 153,600 samples

        logger.info(f"Generating mock channel data: {len(channels)} channels, {num_samples} samples per channel")

        channel_data = {}

        for i, channel in enumerate(channels):
            # Create time array
            t = np.linspace(0, duration_seconds, num_samples)

            # Generate realistic EEG signal with multiple frequency components
            # Delta (0.5-4 Hz)
            delta = 15 * np.sin(2 * np.pi * 2 * t + i * 0.1)

            # Theta (4-8 Hz)
            theta = 12 * np.sin(2 * np.pi * 6 * t + i * 0.2)

            # Alpha (8-13 Hz) - dominant in posterior channels
            alpha_strength = 20 if "O1" in channel or "O2" in channel else 10
            alpha = alpha_strength * np.sin(2 * np.pi * 10 * t + i * 0.3)

            # Beta (13-30 Hz) - more prominent in frontal channels
            beta_strength = 8 if "F3" in channel or "F4" in channel or "F7" in channel or "F8" in channel else 5
            beta = beta_strength * np.sin(2 * np.pi * 20 * t + i * 0.4)

            # Gamma (30-100 Hz)
            gamma = 3 * np.sin(2 * np.pi * 50 * t + i * 0.5)

            # Add random noise
            noise = np.random.randn(num_samples) * 2

            # Combine all components
            signal = delta + theta + alpha + beta + gamma + noise

            # Add occasional artifacts (eye blinks, muscle activity)
            if "FP" in channel or "F3" in channel or "F4" in channel:
                # Eye blink artifacts in frontal channels
                for _ in range(10):  # Add 10 blinks
                    blink_time = np.random.randint(0, num_samples - 256)
                    signal[blink_time:blink_time + 50] += np.random.randn() * 50

            # Apply bandpass filter simulation (keep signal in realistic range)
            signal = np.clip(signal, -100, 100)

            # Downsample for API response (keep it manageable)
            downsampled_signal = signal[::int(num_samples/10000)][:10000]
            channel_data[channel] = downsampled_signal.tolist()

        return channel_data

    def _get_mock_hfo_events(self) -> list:
        """Generate realistic mock HFO events distributed throughout the recording."""
        import numpy as np

        hfo_events = []
        # Updated to match the new channel names
        channels_with_hfos = ["F7-T3", "T3-T5", "F8-T4", "T4-T6", "F3-C3", "C3-P3", "F4-C4", "C4-P4",
                              "CH1-CH2", "CH2-CH3", "CH3-CH4", "CH4-CH5"]
        duration_seconds = 600  # Match the 10-minute recording

        for channel in channels_with_hfos[:10]:
            # Generate 15-25 HFOs per channel, distributed across the recording
            num_hfos = np.random.randint(15, 25)

            for i in range(num_hfos):
                # Distribute HFOs throughout the recording
                start_time = np.random.uniform(5, duration_seconds - 5)
                duration = np.random.uniform(0.01, 0.05)  # 10-50ms duration

                hfo_events.append({
                    "channel": channel,
                    "start_time": round(start_time, 3),
                    "end_time": round(start_time + duration, 3),
                    "peak_frequency": np.random.randint(80, 250),
                    "amplitude": round(np.random.uniform(10, 50), 1)
                })

        # Sort by start time
        hfo_events.sort(key=lambda x: x["start_time"])

        return hfo_events

    async def get_results(self, job_id: str, job_status: str) -> dict | None:
        """Get analysis results for a job."""
        # Remove mock results - always use real data
        # if job_id == "job-1":
        #     result = MOCK_RESULTS["job-1"].copy()
        #     result["channel_data"] = self._get_mock_channel_data()
        #     result["hfo_events"] = self._get_mock_hfo_events()
        #     return result

        if job_status != "completed":
            return None

        # Get results from S3
        results_key = f"results/{job_id}/analysis_results.json"

        try:
            response = self.aws.s3.get_object(
                Bucket=self.aws.s3_bucket_name,
                Key=results_key
            )
            results = json.loads(response["Body"].read())

            # Add download URLs
            results["download_urls"] = {
                "results_json": self.generate_download_url(job_id, "json"),
                "hfo_events_csv": self.generate_download_url(job_id, "csv")
            }

            return results

        except ClientError:
            logger.error(f"Results not found for job {job_id}")
            return None

    def generate_download_url(self, job_id: str, format: str) -> str:
        """Generate presigned URL for downloading results."""
        key = (
            f"results/{job_id}/hfo_events.csv"
            if format == "csv"
            else f"results/{job_id}/analysis_results.json"
        )

        return self.aws.s3.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": self.aws.s3_bucket_name,
                "Key": key,
                "ResponseContentDisposition": f'attachment; filename="{job_id}.{format}"'
            },
            ExpiresIn=3600,
        )
