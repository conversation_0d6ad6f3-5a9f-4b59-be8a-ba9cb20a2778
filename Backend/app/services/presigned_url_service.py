from typing import Any

from botocore.exceptions import ClientError

from ..config import settings
from ..constants import (
    DEFAULT_EXPIRY_SECONDS,
    S3_CLIENT_METHOD_GET,
    S3_CLIENT_METHOD_UPLOAD_PART,
)
from ..logging_config import get_logger

logger = get_logger(__name__)


class PresignedUrlService:
    def __init__(self, s3_client, bucket_name: str):
        self.s3_client = s3_client
        self.bucket_name = bucket_name

    def _generate_presigned_url(
        self,
        client_method: str,
        params: dict[str, Any],
        expiry: int = DEFAULT_EXPIRY_SECONDS,
    ) -> str:
        try:
            return self.s3_client.generate_presigned_url(
                ClientMethod=client_method,
                Params=params,
                ExpiresIn=expiry,
            )
        except ClientError as e:
            logger.error(
                f"Error generating presigned URL for {client_method}: {e}")
            raise

    def generate_download_url(
        self, key: str, expiry: int = DEFAULT_EXPIRY_SECONDS
    ) -> str:
        params = {"Bucket": self.bucket_name, "Key": key}
        return self._generate_presigned_url(S3_CLIENT_METHOD_GET, params, expiry)

    def generate_multipart_part_url(
        self, key: str, upload_id: str, part_number: int
    ) -> str:
        params = {
            "Bucket": self.bucket_name,
            "Key": key,
            "UploadId": upload_id,
            "PartNumber": part_number,
        }
        return self._generate_presigned_url(
            S3_CLIENT_METHOD_UPLOAD_PART, params, settings.presigned_url_expiry_seconds
        )
