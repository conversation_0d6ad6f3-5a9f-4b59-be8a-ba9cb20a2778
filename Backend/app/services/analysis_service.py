"""
Analysis service for HFO processing job management.
"""

import json
import os
import uuid
from datetime import datetime

import boto3
from botocore.exceptions import ClientError
from boto3.dynamodb.conditions import Key

from ..config import settings
from ..logging_config import get_logger
from ..utils.dynamodb_utils import sanitize_for_dynamodb

logger = get_logger(__name__)

# AWS resource configuration
dynamodb = boto3.resource("dynamodb")
sqs = boto3.client("sqs")
s3 = boto3.client("s3")

# Environment configuration
JOBS_TABLE_NAME = settings.jobs_table_name or "biormika-analysis-jobs"
PREFERENCES_TABLE_NAME = settings.preferences_table_name or "biormika-user-preferences"
SQS_QUEUE_URL = settings.sqs_queue_url or ""
S3_BUCKET_NAME = settings.s3_bucket_name
DEFAULT_SENDER_EMAIL = settings.ses_sender_email
DEFAULT_RECEIVER_EMAIL = os.getenv(
    "DEFAULT_RECEIVER_EMAIL", "<EMAIL>")
MAX_BATCH_SQS_MESSAGES = 10
PRESIGNED_URL_EXPIRY = 3600

# Table references
jobs_table = dynamodb.Table(JOBS_TABLE_NAME) if JOBS_TABLE_NAME else None
preferences_table = dynamodb.Table(
    PREFERENCES_TABLE_NAME) if PREFERENCES_TABLE_NAME else None


class AnalysisService:
    """Service for managing HFO analysis jobs."""

    @staticmethod
    async def get_email_preferences(user_id: str) -> tuple[str, str]:
        """Get sender and receiver emails from preferences table."""
        sender_email = DEFAULT_SENDER_EMAIL
        receiver_email = DEFAULT_RECEIVER_EMAIL

        try:
            if preferences_table:
                response = preferences_table.get_item(Key={"user_id": user_id})
                item = response.get("Item")
                if item:
                    sender_email = item.get(
                        "sender_email") or item.get("email") or DEFAULT_SENDER_EMAIL
                    receiver_email = item.get(
                        "receiver_email") or item.get("email") or DEFAULT_RECEIVER_EMAIL
        except Exception as e:
            logger.warning(f"Error getting user email preferences: {e}")

        return sender_email, receiver_email

    @staticmethod
    def validate_file_exists(file_key: str) -> bool:
        """Validate that a file exists in S3."""
        try:
            s3.head_object(Bucket=S3_BUCKET_NAME, Key=file_key)
            return True
        except ClientError:
            return False

    @staticmethod
    def create_job_item(
        job_id: str,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None,
        batch_id: str | None = None
    ) -> dict:
        """Create a job item for DynamoDB."""
        timestamp = datetime.utcnow().isoformat()
        job_item = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": parameters or {},
        }
        if batch_id:
            job_item["batch_id"] = batch_id
        return job_item

    @staticmethod
    def create_sqs_message(
        job_id: str,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None,
        batch_id: str | None = None
    ) -> dict:
        """Create an SQS message for job processing."""
        message = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "parameters": parameters or {},
        }
        if batch_id:
            message["batch_id"] = batch_id
        return message

    @classmethod
    async def submit_single_job(
        cls,
        file_key: str,
        parameters: dict | None = None,
        user_email: str | None = None
    ) -> dict:
        """Submit a single analysis job."""
        # Validate file
        if not cls.validate_file_exists(file_key):
            raise ValueError(f"File not found: {file_key}")

        # Get email preferences
        sender_email, receiver_email = await cls.get_email_preferences("default_user")

        if user_email:
            receiver_email = user_email

        # Create job
        job_id = str(uuid.uuid4())
        job_item = cls.create_job_item(
            job_id, file_key, sender_email, receiver_email, parameters)

        # Save to DynamoDB
        if jobs_table:
            jobs_table.put_item(Item=sanitize_for_dynamodb(job_item))

        # Send to SQS
        if SQS_QUEUE_URL:
            message = cls.create_sqs_message(
                job_id, file_key, sender_email, receiver_email, parameters)
            sqs.send_message(
                QueueUrl=SQS_QUEUE_URL,
                MessageBody=json.dumps(message),
            )

        logger.info(f"Submitted analysis job: {job_id}")
        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Analysis job submitted successfully",
        }

    @classmethod
    async def submit_batch_jobs(
        cls,
        file_keys: list[str],
        parameters: dict | None = None,
        user_email: str | None = None
    ) -> dict:
        """Submit multiple analysis jobs as a batch."""
        # Validate all files
        invalid_files = [
            key for key in file_keys if not cls.validate_file_exists(key)]
        if invalid_files:
            raise ValueError(f"Files not found: {', '.join(invalid_files)}")

        # Get email preferences
        sender_email, receiver_email = await cls.get_email_preferences("default_user")

        if user_email:
            receiver_email = user_email

        # Create batch
        batch_id = str(uuid.uuid4())
        job_ids = []
        messages = []

        for file_key in file_keys:
            job_id = str(uuid.uuid4())
            job_ids.append(job_id)

            # Create job item
            job_item = cls.create_job_item(
                job_id, file_key, sender_email, receiver_email, parameters, batch_id)

            # Save to DynamoDB
            if jobs_table:
                jobs_table.put_item(Item=sanitize_for_dynamodb(job_item))

            # Prepare SQS message
            message = cls.create_sqs_message(
                job_id, file_key, sender_email, receiver_email, parameters, batch_id)
            messages.append({
                "Id": job_id,
                "MessageBody": json.dumps(message),
            })

        # Send batch messages to SQS
        if SQS_QUEUE_URL and messages:
            for i in range(0, len(messages), MAX_BATCH_SQS_MESSAGES):
                batch = messages[i:i+MAX_BATCH_SQS_MESSAGES]
                sqs.send_message_batch(QueueUrl=SQS_QUEUE_URL, Entries=batch)

        logger.info(
            f"Submitted batch analysis: {batch_id} with {len(job_ids)} jobs")
        return {
            "batch_id": batch_id,
            "job_ids": job_ids,
            "status": "pending",
            "message": f"Batch analysis submitted with {len(job_ids)} files",
        }

    @staticmethod
    def get_job_by_id(job_id: str) -> dict | None:
        """Get job details from DynamoDB."""
        if not jobs_table:
            return None

        response = jobs_table.get_item(Key={"job_id": job_id})
        return response.get("Item")

    @staticmethod
    def get_batch_jobs(batch_id: str) -> list[dict]:
        """Get all jobs in a batch."""
        if not jobs_table:
            return []

        response = jobs_table.query(
            IndexName="BatchJobIndex",
            KeyConditionExpression="batch_id = :batch_id",
            ExpressionAttributeValues={":batch_id": batch_id},
        )
        return response.get("Items", [])

    @staticmethod
    def summarize_batch_status(jobs: list[dict]) -> dict:
        """Summarize the status of batch jobs."""
        total = len(jobs)
        status_counts = {
            "completed": sum(1 for j in jobs if j["status"] == "completed"),
            "failed": sum(1 for j in jobs if j["status"] == "failed"),
            "processing": sum(1 for j in jobs if j["status"] == "processing"),
            "pending": sum(1 for j in jobs if j["status"] == "pending"),
        }

        return {
            "total_jobs": total,
            **status_counts,
            "jobs": [
                {
                    "job_id": j["job_id"],
                    "file_key": j.get("file_key"),
                    "status": j["status"],
                    "hfo_count": j.get("hfo_count"),
                    "error": j.get("error_message"),
                }
                for j in jobs
            ],
        }

    @staticmethod
    def get_results_from_s3(job_id: str) -> dict:
        """Get analysis results from S3."""
        results_key = f"results/{job_id}/analysis_results.json"

        response = s3.get_object(Bucket=S3_BUCKET_NAME, Key=results_key)
        results = json.loads(response["Body"].read())

        # Add download URLs
        results["download_urls"] = {
            "results_json": s3.generate_presigned_url(
                "get_object",
                Params={"Bucket": S3_BUCKET_NAME, "Key": results_key},
                ExpiresIn=PRESIGNED_URL_EXPIRY,
            ),
            "hfo_events_csv": s3.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": S3_BUCKET_NAME,
                    "Key": f"results/{job_id}/hfo_events.csv",
                },
                ExpiresIn=PRESIGNED_URL_EXPIRY,
            ),
        }

        return results

    @staticmethod
    def generate_download_url(job_id: str, format: str = "json") -> str:
        """Generate presigned URL for downloading results."""
        # Determine file key based on format
        if format == "csv":
            key = f"results/{job_id}/hfo_events.csv"
            filename = f"{job_id}_hfo_events.csv"
        elif format == "report":
            key = f"results/{job_id}/analysis_report.csv"
            filename = f"{job_id}_comprehensive_report.csv"
        else:
            key = f"results/{job_id}/analysis_results.json"
            filename = f"{job_id}_results.json"

        return s3.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": S3_BUCKET_NAME,
                "Key": key,
                "ResponseContentDisposition": f'attachment; filename="{filename}"'
            },
            ExpiresIn=PRESIGNED_URL_EXPIRY,
        )

    @staticmethod
    def get_mock_results(job_id: str) -> dict | None:
        """Get mock results for demo purposes."""
        if job_id != "job-1":
            return None

        return {
            "metadata": {
                "filename": "sample1.edf",
                "sampling_rate": 256,
                "duration_seconds": 600,
                "channels": ["C3", "C4", "F3", "F4", "O1", "O2"],
                "processing_time": 45.2
            },
            "statistics": {
                "total_hfos": 127,
                "hfo_density": 12.7,
                "channels_with_hfos": ["C3", "C4", "F3"],
                "hfo_rate_per_channel": {
                    "C3": 45,
                    "C4": 52,
                    "F3": 30,
                    "F4": 0,
                    "O1": 0,
                    "O2": 0
                }
            },
            "channel_data": {
                "C3": [0.5, 0.8, 1.2, 0.9, 0.3, -0.2, -0.5, 0.1] * 256,
                "C4": [0.3, 0.6, 1.0, 0.7, 0.2, -0.3, -0.4, 0.0] * 256,
                "F3": [0.4, 0.7, 1.1, 0.8, 0.25, -0.25, -0.45, 0.05] * 256,
                "F4": [0.2, 0.5, 0.9, 0.6, 0.15, -0.35, -0.5, -0.05] * 256,
                "O1": [0.1, 0.4, 0.8, 0.5, 0.1, -0.4, -0.6, -0.1] * 256,
                "O2": [0.0, 0.3, 0.7, 0.4, 0.0, -0.5, -0.7, -0.2] * 256
            },
            "hfo_events": [
                {"channel": "C3", "start_time": 10.5, "end_time": 10.52,
                    "peak_frequency": 85, "amplitude": 15.3},
                {"channel": "C3", "start_time": 25.3, "end_time": 25.33,
                    "peak_frequency": 92, "amplitude": 18.7},
                {"channel": "C4", "start_time": 15.7, "end_time": 15.72,
                    "peak_frequency": 88, "amplitude": 16.2},
                {"channel": "C4", "start_time": 30.2, "end_time": 30.23,
                    "peak_frequency": 95, "amplitude": 20.1},
                {"channel": "F3", "start_time": 45.9, "end_time": 45.93,
                    "peak_frequency": 82, "amplitude": 14.8}
            ]
        }

    @staticmethod
    def get_user_jobs(user_email: str | None = None) -> list[dict]:
        """Get jobs from DynamoDB. Optionally filter by user email."""
        if not jobs_table:
            logger.warning("Jobs table not available")
            return []

        try:
            if user_email:
                response = jobs_table.query(
                    IndexName="UserEmailIndex",
                    KeyConditionExpression=Key("user_email").eq(user_email),
                    ScanIndexForward=False,
                )

                jobs = response.get("Items", [])

                while "LastEvaluatedKey" in response:
                    response = jobs_table.query(
                        IndexName="UserEmailIndex",
                        KeyConditionExpression=Key("user_email").eq(user_email),
                        ScanIndexForward=False,
                        ExclusiveStartKey=response["LastEvaluatedKey"],
                    )
                    jobs.extend(response.get("Items", []))

                logger.info(f"Found {len(jobs)} jobs for user {user_email}")
                return jobs

            # No user filter – scan entire table and sort by creation time
            response = jobs_table.scan()
            jobs = response.get("Items", [])

            while "LastEvaluatedKey" in response:
                response = jobs_table.scan(
                    ExclusiveStartKey=response["LastEvaluatedKey"],
                )
                jobs.extend(response.get("Items", []))

            jobs.sort(key=lambda item: item.get("created_at", ""), reverse=True)
            logger.info(f"Found {len(jobs)} jobs from scan")
            return jobs

        except Exception as e:
            logger.error(
                "Error retrieving jobs%s: %s",
                f" for user {user_email}" if user_email else "",
                e,
            )
            return []

    @staticmethod
    def get_mock_jobs() -> list[dict]:
        """Get mock job list for demo purposes."""
        return [
            {
                "job_id": "job-3",
                "status": "pending",
                "file_key": "edf-files/sample3.edf",
                "created_at": "2025-09-22T15:31:00Z"
            }
        ]
