"""
API endpoints for user preferences management.
"""


from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field, validator

from ..logging_config import get_logger
from ..services.user_service import UserService

logger = get_logger(__name__)

router = APIRouter(prefix="/user", tags=["user"])


# Request/Response models
class UserPreferences(BaseModel):
    """User preferences model."""
    user_id: str
    sender_email: str
    receiver_email: str
    notification_enabled: bool = True
    created_at: str | None = None
    updated_at: str | None = None

    @validator("sender_email", "receiver_email")
    def validate_email(cls, v):
        """Validate email format."""
        if not UserService.validate_email_format(v):
            raise ValueError("Invalid email format")
        return v.lower()


class UpdatePreferencesRequest(BaseModel):
    """Request model for updating preferences."""
    sender_email: str = Field(..., description="Email used to send notifications")
    receiver_email: str = Field(..., description="Email receiving notifications")
    notification_enabled: bool = Field(True, description="Enable email notifications")

    @validator("sender_email", "receiver_email")
    def validate_email(cls, v):
        """Validate email format."""
        if not UserService.validate_email_format(v):
            raise ValueError("Invalid email format")
        return v.lower()


class PreferencesResponse(BaseModel):
    """Response model for preferences."""
    sender_email: str
    receiver_email: str
    notification_enabled: bool
    last_updated: str


@router.get("/preferences", response_model=PreferencesResponse)
async def get_user_preferences(user_id: str = "default_user"):
    """Get user preferences including email settings."""
    try:
        preferences = UserService.get_user_preferences(user_id)
        return PreferencesResponse(**preferences)

    except Exception as e:
        logger.error(f"Error getting user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.put("/preferences", response_model=PreferencesResponse)
async def update_user_preferences(
    request: UpdatePreferencesRequest,
    user_id: str = "default_user"
):
    """Update user preferences including email settings."""
    try:
        preferences = UserService.update_user_preferences(
            user_id=user_id,
            sender_email=request.sender_email,
            receiver_email=request.receiver_email,
            notification_enabled=request.notification_enabled
        )

        return PreferencesResponse(**preferences)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error updating user preferences: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.post("/preferences/verify-email")
async def verify_email(sender_email: str = Query(..., alias="email"), user_id: str = "default_user"):
    """Verify email address (send test email)."""
    try:
        # Validate email
        if not UserService.validate_email_format(sender_email):
            raise HTTPException(status_code=400, detail="Invalid email format")

        # Send verification email
        result = UserService.send_verification_email(sender_email)
        return result

    except ValueError as e:
        if "SES sandbox" in str(e):
            raise HTTPException(status_code=400, detail=str(e)) from e
        else:
            raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"Error verifying email: {e}")
        raise HTTPException(status_code=500, detail=str(e)) from e


@router.get("/stats")
async def get_user_stats(user_id: str = "default_user"):
    """Get user analysis statistics."""
    try:
        return UserService.get_user_statistics(user_id)

    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        # Return empty stats on error
        return {
            "total_analyses": 0,
            "hfos_detected": 0,
        }
