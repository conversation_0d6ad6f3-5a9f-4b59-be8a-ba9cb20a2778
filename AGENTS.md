# Repository Guidelines

## Project Structure & Module Organization
- `Frontend/`: Vite-powered React + TypeScript UI; components live under `src/components`, Redux slices under `src/store`, and shared types in `src/types`.
- `Backend/`: FastAPI service packaged for Lambda; core logic sits in `app/` while `run.py` starts the local server and `build_lambda.sh` prepares deployments.
- `HFOProcessor/`: Python signal-processing engine; detection pipeline in `core/` and AWS adapters in `services/` with `processor.py` as the CLI entry point.
- `Infra/`: AWS CDK app defining buckets, queues, Lambda, and ECS; `infra/` holds construct modules and `cdk-outputs.json` feeds runtime configs.
- Root `deploy_*.sh` scripts chain infra, backend, frontend, and processor releases for consistent rollouts.

## Build, Test & Development Commands
Run component-specific commands from their directories after installing dependencies.
```bash
# Frontend
npm install && npm run dev        # Local UI at http://localhost:5173
npm run build && npm run lint

# Backend
python3 -m venv venv && source venv/bin/activate
python run.py                     # Swagger at http://localhost:8000/docs
pytest                            # Backend test suite

# HFO Processor
docker build -t biormika-hfo .
python processor.py --test        # Sample pipeline smoke test

# Infrastructure
python3 -m venv .venv && source .venv/bin/activate
cdk diff && cdk deploy --profile biormika
```

## Coding Style & Naming Conventions
- Python (Backend/Infra): 4-space indent, type-hinted functions, modules and files snake_case; format with `ruff format .` and lint via `ruff check .`.
- Frontend: ESLint enforces TypeScript best practices; React components PascalCase, hooks named `useSomething`, Redux slices camelCase, and prefer Tailwind utilities over bespoke CSS.
- Configuration files and generated artifacts stay out of version control; copy `.env.example` into `.env` locally and keep secrets in AWS Parameter Store.

## Testing Guidelines
- Backend and Infra use `pytest`; add tests under `tests/` with files named `test_*.py` and fixtures in `conftest.py` when shared setup is needed.
- Expand HFO coverage with deterministic EDF fixtures and assertions around both CSV outputs and S3 interactions; keep CLI smoke test (`--test`) green.
- Frontend currently lacks an automated suite—when adding tests, adopt Vitest + React Testing Library under `src/__tests__/` and include rendering or store interaction checks.
- Document manual smoke steps for uploads and analysis flows in PRs whenever automated coverage is absent.

## Commit & Pull Request Guidelines
- Follow the existing imperative, sentence-case commit style (e.g., `Add analysis page routing`); keep commits scoped to a single concern and reference directories when helpful.
- Before opening a PR, run `npm run lint`, `pytest`, `ruff check .`, and any relevant `cdk` command; note the executed checks in the PR description.
- PRs should link Jira/GitHub issues, call out config or schema changes, and attach screenshots/GIFs for UI work or JSON samples for API changes.
- Request at least one reviewer from the owning area (Frontend, Backend, Infra, or Processor) and wait for green CI prior to merging.

## Security & Configuration Tips
- Never commit secrets or AWS credentials; rely on `.env` files ignored by git and CDK outputs for resource identifiers.
- Rotate IAM credentials used in `deploy_*.sh` scripts regularly and prefer role-based access when running in CI.
- Restrict large EDF fixtures to S3 buckets; store only minimal anonymized samples in the repo to keep clones lightweight.
