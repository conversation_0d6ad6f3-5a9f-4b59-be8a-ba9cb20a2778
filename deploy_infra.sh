#!/bin/bash

# Deploy Infrastructure Script
# Deploys AWS infrastructure using CDK

set -e

echo "🚀 Starting Infrastructure Deployment..."
echo "========================================"

# Check if running from root directory
if [ ! -d "Infra" ]; then
    echo "❌ Error: Must run from project root directory"
    exit 1
fi

# Navigate to Infrastructure directory
cd Infra

# Check if virtual environment exists
if [ ! -d ".venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source .venv/bin/activate

# Install/update dependencies
echo "📦 Installing dependencies..."
pip install -q -r requirements.txt -r requirements-dev.txt

# Run linting
echo "🔍 Running linting checks..."
ruff check . || true

# Synthesize CDK stack
echo "🏗️  Synthesizing CDK stack..."
cdk synth

# Deploy with approval prompt
echo ""
echo "🚀 Deploying infrastructure to AWS..."
echo "This will:"
echo "  - Deploy/update S3 buckets"
echo "  - Deploy/update Lambda function"
echo "  - Deploy/update API Gateway"
echo "  - Deploy/update CloudFront distribution"
echo ""

read -p "Do you want to continue? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    cdk deploy --profile biormika --outputs-file cdk-outputs.json

    echo ""
    echo "✅ Infrastructure deployment complete!"
    echo ""
    echo "📋 Outputs saved to: Infra/cdk-outputs.json"
    echo ""

    # Extract and display key outputs
    if [ -f "cdk-outputs.json" ]; then
        echo "🔑 Key Endpoints:"
        echo "=================="
        grep -E "(CloudFrontURL|ApiGatewayUrl|BucketName)" cdk-outputs.json | sed 's/.*"BiormikaStack\./  /' | sed 's/":/: /' | sed 's/[",]//g'
    fi
else
    echo "❌ Deployment cancelled"
    exit 0
fi

# Return to root
cd ..